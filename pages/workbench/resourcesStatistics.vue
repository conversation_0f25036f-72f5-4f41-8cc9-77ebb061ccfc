<template>
	<view class="u-page">
		<u-cell :titleStyle="{fontWeight: 500}" title="资源能力">
			<image slot="icon" class="u-cell-icon" src="@/static/tabbar/select.png" mode="widthFix"></image>
			<u-checkbox-group slot="value" name="allCheck" @change="expand">
				<u-checkbox customStyle="margin:0 0 0 0" :checked="open" shape="circle" label="展开">
				</u-checkbox>
			</u-checkbox-group>
		</u-cell>
		<view class="u-page__item">
			<u-collapse v-model="activeNames" ref="collapseView">
				<view v-for="item in dataMap">
					<u-collapse-item name="bmzy" :open="true" disbled="true">
						<view slot="title" style="display: flex;">
							<image style="width: 25px;height: 25px;" class="u-cell-icon"
								src="/static/asset/zichanshiyong.png" mode="center"></image>
							<text style="padding-left: 10px;margin-top: 2px;">{{item.belongAbility}}</text>
						</view>
						<text class="u-collapse-content">
							总数： <text class="count_title_1">{{item.assetTotal}}</text>
						</text>
						<text class="u-collapse-content">
							正常： <text class="count_title_2">{{item.noalarmAssetNum}}</text>
						</text>
						<text class="u-collapse-content">
							异常： <text class="count_title_3">{{item.alarmAssetNum}}</text>
						</text>
					</u-collapse-item>
				</view>
			</u-collapse>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				open: true,
				activeNames: ["bmzy"],
				dataMap: [],
			}
		},
		onLoad() {
			this.$H.checkLoginAndJumpStart();//检查登录状态
			this.getAssetCountNum();
		},
		onReady() {
			setTimeout(() => {
				this.$refs.collapseView.init();
			}, 500);
		},
		computed: {
			getIcon() {
				return path => {
					return '/static/asset/' + path + '.png';
				}
			},
		},
		mounted() {
			// console.log('子组件', this.$refs.collapseView)
		},
		methods: {
			expand() {
				if(this.open){
					this.activeNames = [];
				}else{
					this.activeNames = ["bmzy"];
				}
				this.open = !this.open;
				this.$nextTick(() => {
					// 必要：nextTick延迟回调，否则组件不会刷新
					this.$refs.collapseView.init()
				})
			},
			// 按能力统计
			getAssetCountNum() {
				let current = this;
				current.$H.get('lbeam-core/asset/asset-agg/getAssetCountNum').then(res => {
					if (res.status == "0") {
						this.dataMap = res.data;
					}
				});
			},
		}
	}
</script>

<style>
	.count_title_1 {
		color: darkslateblue;
		/* font-weight: bolder; */
		margin-left: 10px;
		line-height: 20px;
	}

	.count_title_2 {
		color: limegreen;
		/* font-weight: bolder; */
		margin-left: 10px;
		line-height: 21px;
	}

	.count_title_3 {
		color: crimson;
		/* font-weight: bolder; */
		margin-left: 10px;
		line-height: 21px;
	}
</style>
