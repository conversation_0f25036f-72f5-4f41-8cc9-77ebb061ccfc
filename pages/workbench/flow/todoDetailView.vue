<template>
	<view class="u-page">
		<view style="display: flex;justify-content: center;">
			<text class="u-demo-block__title" style="font-size: 22px;">{{title}}</text>
		</view>
		<u-line color="#2979ff"></u-line>
		<view class="u-demo-block">
			<view style="display: flex;margin-top: 10px;">
				<!-- <u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon> -->
				<text class="u-demo-block__title"
					style="margin-top: 7px;margin-left: 5px;font-weight: bold;">基本信息</text>
			</view>
			<view class="u-demo-block__content">
				<!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
				<u--form labelPosition="left" :model="flowItemInfo" ref="form1">
					<view v-for="processCol in basicColumns">
						<u-form-item :label="processCol.columnComment" v-show="processCol.attrs.hidden != 1"
							labelWidth="120" prop="flowItemInfo.processMain[processCol.javaField]">
							<view v-if="processCol.htmlType == 'input' || processCol.htmlType == 'number'" style="width: 90%;">
								<u--input v-model="flowItemInfo.processMain[processCol.javaField]" disabled
									style="height: 13px;margin-left: -3px;"></u--input>
							</view>
						</u-form-item>
					</view>
					<u-form-item label="流程图" prop="hotel" labelWidth="120" borderBottom @click="openPopup()">
						<u--input disabled disabledColor="#ffffff" placeholder="点击查看" border="none">
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<view style="display: flex;margin-top: 10px;">
						<!-- <u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon> -->
						<text class="u-demo-block__title"
							style="margin-top: 7px;margin-left: 5px;font-weight: bold;">业务信息</text>
					</view>
					<view v-for="processCol in bussColumns">
						<u-form-item :label="processCol.columnComment" v-show="processCol.attrs.hidden != 1"
							labelWidth="120" prop="flowItemInfo.processMain[processCol.javaField]">
							<view v-if="processCol.htmlType == 'input'" style="width: 90%;">
								<u--input v-model="flowItemInfo.processMain[processCol.javaField]"
									disabled style="height: 13px;margin-left: -3px;">
								</u--input>
							</view>
							<view v-if="processCol.htmlType == 'select'" style="width: 90%;">
								<u--input v-model="flowItemInfo.processMain[processCol.javaField + 'Name']"
									disabled style="height: 13px;margin-left: -3px;">
								</u--input>
							</view>
							<view v-if="processCol.htmlType == 'datetime'" style="width: 90%;">
								<view>
									{{flowItemInfo.processMain[processCol.javaField]}}
								</view>
							</view>
							<view v-if="processCol.htmlType == 'textarea' || processCol.htmlType == 'richTextarea'"
								style="width: 90%;">
								<u--textarea v-model="flowItemInfo.processMain[processCol.javaField]"
									disabled style="height: 70px;"></u--textarea>
							</view>
							<view v-if="processCol.htmlType == 'number'" style="width: 90%;">
								<u--input v-model="flowItemInfo.processMain[processCol.javaField]"
									disabled></u--input>
							</view>
						</u-form-item>
					</view>
					<u-line></u-line>
					<!-- <view style="display: flex;margin-top: 10px;">
						<u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon>
						<text class="u-demo-block__title"
							style="margin-top: 7px;margin-left: 5px;font-weight: bold;">操作信息</text>
					</view>
					<u-form-item label="审批意见" labelWidth="120" borderBottom style="width: 93%;">
						<u--textarea placeholder="" v-model="approve" disabled count></u--textarea>
					</u-form-item> -->
					<u-form-item label="受理人" prop="hotel" labelWidth="120" borderBottom
						v-show="operateValue != '归档'">
						<u--input v-model="processSelected" disabled disabledColor="#ffffff" border="none">
						</u--input>
					</u-form-item>
				</u--form>
				<view style="display: flex;justify-content: right;margin-top: 20px; ">
					<u-button type="error" style="width: 25%;" text="关闭" @click="goBack()"></u-button>
				</view>
			</view>
		</view>
		<u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" :mode="popupData.mode" :show="show" height="2500"
			:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
			:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="show = false">
			<view class="u-popup-slot" :style="{
				width: '100vw',
				height: '100vh',
				overflow: auto,
				marginTop: '20px',marginLeft: '30px'
			}">
				<view>
					<view style="display: flex;justify-content: center;">
						<text class="u-demo-block__title" style="font-size: 20px;">流程图</text>
					</view>
					<u-line customStyle="margin:10px 0 10px -15px"></u-line>
				</view>
				<scroll-view scroll-x="true" class="flow-chart-scroll">
					<process-svg ref="processSvg" :flow-svg="flowSvg" :process-records="processRecords"></process-svg>
				</scroll-view>
				<view style="margin-left: 20px;margin-right: 20px;">
					<u-form-item label="流程记录" prop="hotel" labelWidth="80" borderBottom @click="showMoreRecords()">
						<u-icon slot="right" name="arrow-up" v-show="!showMoreRecord"></u-icon>
						<u-icon slot="right" name="arrow-down" v-show="showMoreRecord"></u-icon>
					</u-form-item>
				</view>
				<view class="record-scroll">
					<u-steps :current="processRecords.length - 1" direction="column" v-for="(item,index) in processRecords"
						:style="{height: windowHeight}">
						<u-steps-item :title="item.fromTaskName">
							<text slot="desc" class="u-demo-block__title" style="margin-top: 2px;"></text>
							<text slot="desc" class="u-demo-block__title" v-show="showMoreRecord">到达时间：{{item.updateTime}}</text>
							<text slot="desc" class="u-demo-block__title" v-show="showMoreRecord">受理时间：{{item.claimTime}}</text>
							<text slot="desc" class="u-demo-block__title">处理时间：{{item.createTime}}</text>
							<text slot="desc" class="u-demo-block__title">处理人：{{item.createByUsername}}</text>
							<text slot="desc" class="u-demo-block__title" v-show="showMoreRecord">处理单位：{{item.createByDeptname}}</text>
							<text slot="desc" class="u-demo-block__title" v-show="index != 0">处理意见：{{item.approved == null?"无":item.approved}}</text>
						</u-steps-item>
					</u-steps>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	const svgApi = require("@/common/loadSvg.js")
	import processSvg from "./processSvg.vue"
	export default {
		components: {
			LyTree,
			processSvg
		},
		data() {
			return {
				title: '',
				allSelectFlag: true,
				treeDataMap: [],
				checkboxValue: [],
				flowSvg: undefined,
				processRecords: [],
				processors: [],
				processorsTreeNode: [],
				processSelected: [], //选择的受理人中文名称列表
				processSelectedIds: [], //选择的受理人ID列表
				depts: [], //组织树
				deptId: '', //当前选择的部门Id
				deptIdUndetermined: '', //组织树选择的部门Id，只选树，没选人时，当前为待确认状态
				approve: '',
				show: false,
				showMoreRecord: false,
				flowItemInfo: {},
				popupData: {
					overlay: true,
					mode: 'right',
					borderRadius: '',
					round: 10,
					closeable: true,
					closeOnClickOverlay: true
				},
				operateValue: '',
				windowHeight: '',
				processorActionSheet: false,
				basicColumns: [], //基本信息
				bussColumns: [], //业务信息
				procKey: '',
			}
		},
		onLoad(para) {
			uni.showLoading({
				title: '加载中'
			})
			this.title = para.procName;
			this.procKey = para.procKey;
			this.getFlowItem(para.procKey, para.mainId, para.taskId);
			this.getflowSVG(para.procKey, para.mainId);
			this.getDept();
			uni.getSystemInfo({
				success: function(res) {
					this.windowHeight = res.windowHeight - 450;
				}
			})
			setTimeout(function() {
				uni.hideLoading()
			}, 1000)
		},
		methods: {
			// 根据操作选择默认受理人
			selectDefaultProcessorsByOperateName() {
				this.processSelectedIds = [];
				this.processSelected = [];
				let nextActivities = this.flowItemInfo.nextActivities.filter(item => item.operateName == this
					.operateValue);
				this.deptId = nextActivities[0].historyDept; //部门
				this.getRoleUsers(this.deptId);
				setTimeout(() => {
					this.processSelectedIds = nextActivities[0].historyUser; //默认受理人ID
					this.processSelected = this.getUserById(this.processSelectedIds); //默认受理人中文名
					this.checkboxValue = this.processSelectedIds; //复选框选中
					console.log("selectDefaultProcessorsByOperateName info", this.processSelectedIds);
				}, 1500);
			},
			// 点击组织树
			handleNodeClick(obj) {
				//this.checkboxValue = [];
				this.deptIdUndetermined = obj.key;
				this.getRoleUsers(obj.key);
			},
			// 选择受理人
			checkboxChange(n) {
				let current = this;
				current.processSelected = [];
				current.processSelectedIds = [];
				n.forEach((id => {
					let node = current.processors.treeData[0].children.filter(item => item.id == id);
					current.processSelected.push(node[0].label);
					current.processSelectedIds.push(node[0].id);
				}))
				current.deptId = this.deptIdUndetermined; //只有选择人之后，组织（树的选择）才最终确定
			},
			goBack() {
				uni.navigateBack();
			},
			// 获取组织树
			getDept() {
				this.$H.get('/eam-pm/common/getDept/').then(res => {
					if (res.status == "0") {
						console.log("getDept", res.data);
						this.depts = res.data;
					}
				})
			},
			// 获取组织树处理人列表
			getRoleUsers(deptId) {
				this.$H.get('/eam-pm/common/getRoleUsers/', {
					type: 'receiver',
					deptId: deptId,
					procKey: this.procKey,
				}).then(res => {
					if (res.status == "0") {
						console.log("getRoleUsers", res.data, this.checkboxValue);
						this.processors = res.data;
						this.processorsTreeNode = this.processors.treeData[0].children;//指定部门的节点，节点包含该部门人员
					}
				})
			},
			// 获取流程实例消息，并初始化
			getFlowItem(procKey, mainId, taskId) {
				this.$H.get('/eam-pm/sheets/' + procKey + '/todo/detail/' + mainId + '/' + taskId).then(res => {
					if (res.status == "0") {
						console.log("getFlowItem", res.data);
						this.flowItemInfo = res.data;
						this.basicColumns = this.flowItemInfo.processDef.basicColumns;
						this.bussColumns = this.flowItemInfo.processDef.bussColumns;
						this.operateValue = this.flowItemInfo.nextActivities[0].operateName; //默认选第一个操作
						this.selectDefaultProcessorsByOperateName(); //选择默认受理人
					}
				})
			},
			getUserById(ids) {
				let names = [];
				if(ids != null){
					ids.forEach((id => {
						for (const key in this.processors.nameMap) {
							// console.log("key名称是："+key+",key的值是："+this.processors.nameMap[key])
							if (id == key) {
								names.push(this.processors.nameMap[key]);
							}
						}
					}))
				}
				return names;
			},
			// 获取流程图
			getflowSVG(procKey, mainId) {
				this.$H.get('/eam-pm/sheets/' + procKey + '/historyAndsvg/' + mainId).then(res => {
					if (res.status == "0") {
						this.processRecords = res.data.processLinks;
						let approve = '';
						if(res.data.processTasks !== undefined && res.data.processTasks != null && res.data.processTasks.length > 0){
							approve = res.data.processTasks[0].approved;
						}
						this.approve = approve;
						this.flowSvg = svgApi.initFlowSvgExtNode(res.data.svg);
						// console.log('getflowSVG',this.flowSvg);
					}
				})
			},
			drawFlowStates() {
			    this.$nextTick(() => {
					// svgApi.initFlowSvgNodeAttr(this.$refs.svgCanvas.$el);
						// svgApi.loadFlowSvgNode4App(
						// 	this,
						// 	this.processRecords,
						// 	this.$refs.svgCanvas.$el
						// );
						this.$refs.processSvg.drawFlowStates();
				});
			},
			openPopup() {
				uni.$u.sleep().then(() => {
					this.show = !this.show

					if (this.show) {
						// this.queryCategoryCount();
						this.drawFlowStates();
					}
				})
			},
			showMoreRecords() {
				this.showMoreRecord = !this.showMoreRecord;
			},
			close() {
				this.processorActionSheet = false
			},
			select(e) {
				console.log('select', e);
			},
			//打开选择受理人窗口
			selectProcessor() {
				this.processorActionSheet = true;
				this.$nextTick(() => {
					// expand-current-node-parent展开当前节点的父节点
					this.$refs.tree.setCurrentKey(this.deptId);
				});
			},
		}
	}
</script>

<style>
	.long-popup {
		/deep/ .uni-scroll-view-content {
			transform: none !important;
		}
	}

	.flow-chart-scroll {
		width: 95%;
		height: 350px;
		overflow: hidden;
		white-space: nowrap;
		margin-left: 20px;
	}

	.flow-chart-cell {
		display: inline-block;
		width: 100%;
		height: 350px;
	}

	.record-scroll {
		width: 100%;
		height: 30%;
		overflow-y: auto;
		white-space: nowrap;
		margin-left: 20px;
	}

	#main-pop {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
	}

	#left-bar {
		margin-top: -10px;
		height: 50vh;
		flex-grow: 1;
	}

	#right-bar {
		height: 50vh;
		width: 130px;
	}
</style>
