<template>
  <!-- 图表-->
  <view ref="svg" v-html="flowSvg" :prop="data" :change:prop="flow.update">
  </view>
</template>

<!-- 逻辑层 -->
<script>
export default {
  props: {
    flowSvg: String,
    processRecords: Array
  },
  data() {
    return {
	  data: {}
    }
  },
  mounted() {
  	console.log(this.flowSvg);
  },
  methods: {
		getLinks(handleObj) {
		},
		drawFlowStates() {
			this.data = this.processRecords;
		},
  }
}
</script>

<script module="flow" lang="renderjs">
const svgApi = require("@/common/loadSvg.js")
export default {
  name: "processSvgChart.vue",
  props: {
  },
  data() {
	return {
	}
  },
  mounted() {
  },
  methods: {
	  update() {
            let handle = {};
			this.$ownerInstance.callMethod("getLinks", handle);
			this.$nextTick(() => {
				let processRecords = this.data;
				if(!Array.isArray(processRecords)) {
					processRecords = [];
				}
				console.log("============== processRecords 2", processRecords);
				svgApi.loadFlowSvgNode4App(
					 		 this,
					 		 processRecords,
					 		 this.$el
				  );
			})
	  }
  }
}
</script>

<style scoped>

</style>