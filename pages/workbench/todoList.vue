<template>
	<view class="cell-page">
		<u-row>
			<u-col offset="0">
				<u-search style="margin: 30px;width: 70%;" v-model="todoSearch" :show-action="false" @change="search">
				</u-search>
			</u-col>
		</u-row>
		<u-row @click="showMoreRecords()">
			<u-col span="10" customStyle="">
				<text style="font-size: 12px;margin-left: 20px;">共 {{ origDataMap.length }} 条待办</text>
			</u-col>
			<u-col span="4">
				<u-text prefixIcon="arrow-up" style="padding-left: 27px;" v-show="!showMoreRecord"> </u-text>
				<u-text prefixIcon="arrow-down" style="padding-left: 27px;" v-show="showMoreRecord"> </u-text>
			</u-col>
		</u-row>
		<!-- <u-list-item>
			<u-cell>
				<text slot="icon" style="font-size: 12px;margin-top: -20px;">共 {{ origDataMap.length }} 条待办</text>
			</u-cell>
		</u-list-item> -->
		<!-- <u-line customStyle="margin-top: 15px"></u-line> -->
		<u-empty v-if="dataShow" icon="/static/images/data.png">
		</u-empty>
		<u-list customStyle="margin-top: 15px">
			<u-list-item v-for="(item, index) in dataMap" :key="index">
				<u-cell>
					<view slot="title" style="display: flex;" @click="switchFlow(item)">
						<!-- <image style="width: 20px;height: 20px;" class="u-cell-icon" src="/static/asset/text.png" mode="widthFix"></image> -->
						<u-icon name="file-text" size="25" color="#918c99" ></u-icon>
						<text style="padding-left: 10px;margin-top: 1px;color: grey;">{{ item.title }}</text>
					</view>
					<view slot="label" style="margin-top: 10px;" v-show="showMoreRecord">
						<view class="title-flow">
							<view class="title-flow_left">工单编号</view>
							<view class="title-flow_right">{{ item.sheetId}}</view>
						</view>
						<view class="title-flow">
							<view class="title-flow_left">当前环节</view>
							<view class="title-flow_right">{{ item.taskName}}</view>
						</view>
						<view class="title-flow">
							<view class="title-flow_left">派单时间</view>
							<view class="title-flow_right">{{ item.createTime}}</view>
						</view>
						<view class="title-flow">
							<view class="title-flow_left">创建人</view>
							<view class="title-flow_right">{{ item.userName}}</view>
						</view>
						<view class="title-flow">
							<view class="title-flow_left">受理人</view>
							<view class="title-flow_right">{{ item.ownerName}}</view>
						</view>
					</view>
				</u-cell>
			</u-list-item>
		</u-list>
	</view>
</template>

<script>
	import $C from '@/common/config.js'
	export default {
		data() {
			return {
				dataMap: [],
				origDataMap: [], //原始集合
				todoSearch: '',
				dataShow: false,
				showMoreRecord: true,
			}
		},
		onLoad() {
		},
		onShow() {
			this.getTodoList();
			this.$H.checkLoginAndJumpStart();//检查登录状态
		},
		methods: {
			openDetail(item) {
				console.log("flow item info:",item);
				// let detail = this.getFlowItem(item);
				this.getFlowItem(item).then(detail => {
					this.switch(item,detail);
				});
			},
			showMoreRecords() {
				this.showMoreRecord = !this.showMoreRecord;
			},
			switchFlow(item){
				let param = "?procKey=" + item.procKey + "&mainId=" + item.mainId + "&taskId=" + item.taskId + "&procName=" + item.procName;
				this.$H.href("/pages/workbench/flow/todoDetail" + param,1);
			},
			// 当前获取并保存流程实例-备用
			setFlowItem(procKey, mainId, taskId,procName) {
				let that = this;
				that.$H.get('/eam-pm/sheets/' + procKey + '/todo/detail/' + mainId + '/' + taskId).then(res => {
					if (res.status == "0") {
						console.log("getFlowItem", res.data);
						uni.setStorageSync('flowItem', res.data);
						let param = "?procKey=" + procKey + "&mainId=" + mainId + "&taskId=" + taskId + "&procName=" + procName;
						// setTimeout(function() {
							that.$H.href("/pages/workbench/flow/goOnlineApprove/goOnlineApprove" + param,1);
						// }, 1500)
					}
				})
			},
			search(value) {
				this.dataMap = this.origDataMap.filter(item => item.title.includes(value));
			},
			clearBlank(value){
			    if(value){
			        value = value.replace(/\s/g,"")
			    }
			    return value
			},
			getTodoList() {
				let current = this;
				// let param = {
				// 	"taskParticipants": "",
				// 	"sheetId": "",
				// 	"taskName": "",
				// 	"title": "",
				// 	"procName": "",
				// 	"areaCode": "",
				// 	"type": "task",
				// 	"status": 2,
				// 	"orderStatus": "",
				// 	"claimStatus": "",
				// 	"dimensionType": "task",
				// 	"evaluationTag": "",
				// 	"keywords": "",
				// 	// "procNames": "",
				// 	"startTime": "",
				// 	"endTime": "",
				// 	"sheetEndTime": "",
				// 	"sheetStartTime": "",
				// 	"userId": "",
				// 	"ownerId": "",
				// 	"sortField": "",
				// 	"sortType": "",
				// 	"searchType": "person",
				// 	"pageSize": 10,
				// 	"pageNum": 1,
				// };
				// param.token = uni.getStorageSync('token');
				// current.$H.post('eam-pm/common/zsj/commonTaskInfo', param, true).then(res => {
				// 	if (res.status == "succeed") {
				// 		this.dataMap = res.rows;
				// 	}
				// 	console.log("###", res);
				// });
				uni.showLoading({
					title: '加载中'
				})
				setTimeout(() => {
					uni.hideLoading();//加载超时设置
				}, 3000)
				uni.request({
					url: $C.getBaseUrl() + '/eam-pm/common/zsj/commonTaskInfo',
					method:"POST",
					sslVerify: false,
					data:{
						"taskParticipants": "",
						"sheetId": "",
						"taskName": "",
						"title": current.todoSearch,
						"procName": "",
						"areaCode": "",
						"type": "task",
						"status": 2,
						"orderStatus": "",
						"claimStatus": "",
						"dimensionType": "task",
						"evaluationTag": "",
						"keywords": "",
						// "procNames": "",
						"startTime": "",
						"endTime": "",
						"sheetEndTime": "",
						"sheetStartTime": "",
						"userId": "",
						"ownerId": "",
						"sortField": "",
						"sortType": "",
						"searchType": "person",
						"pageSize": 1000,
						"pageNum": 1,
					},
					dataType:'json',//设置json返回值就会json.parse
					header:{
						"Content-Type":"application/x-www-form-urlencoded",
						"Authorization":uni.getStorageSync("token"),
					},
					success(res) {
						uni.hideLoading();
						if (res.data.status == "succeed") {
							current.dataMap = res.data.rows;
							current.origDataMap = res.data.rows;
							console.log(current.dataMap);
							if (res.data.rows.length < 1) {
								current.dataShow = true; //空数据
							} else {
								current.dataShow = false;
							}
						}else{
							uni.showToast({
								title: res.data.msg ? res.data.msg : '请求异常',
								icon: 'none'
							})
						} 
					},
					fail: err => {
						uni.showModal({
							content: err.errMsg ? err.errMsg : "发生异常，请联络管理员"
						})
					},
				})
			},
		}
	}
</script>

<style lang="scss">
	.cell-page {
		padding-bottom: 20px;
	}

	.cell-box {
		&__title {
			font-size: 14px;
			color: rgb(143, 156, 162);
			margin: 20px 0px 0px 15px;
		}

		&__block {
			// background-color: #fff;
			margin-top: 20px;
		}
	}

	.u-page {
		padding: 0;

		&__item {

			&__title {
				color: $u-tips-color;
				background-color: $u-bg-color;
				padding: 15px;
				font-size: 15px;

				&__slot-title {
					color: $u-primary;
					font-size: 14px;
				}
			}
		}
	}

	.u-slot-title {
		@include flex;
		flex-direction: row;
		align-items: center;
	}

	.u-cell-text {
		font-size: 15px;
		line-height: 22px;
		color: #303133;
		margin-right: 5px;
	}

	.u-slot-value {
		line-height: 17px;
		text-align: center;
		font-size: 10px;
		padding: 0 5px;
		height: 17px;
		color: #FFFFFF;
		border-radius: 100px;
		background-color: #f56c6c;
	}
	
	.title-flow {
		display: flex;width: 100%;
		
		&_left {
			font-size: 14px;color: #55557f;margin-left: 10px;width: 70px;
		}
		
		&_right {
			font-size: 14px;color: #00557f;margin-left: 10px;
		}
	}
	 
</style>
