<template>
	<view class="u-page">
		<u-cell :titleStyle="{fontWeight: 500}" @click="openPopup()" title="资源类型" isLink>
			<image slot="icon" class="u-cell-icon" src="../../static/tabbar/select.png" mode="widthFix"></image>
		</u-cell>
		<u-empty v-if="dataShow" icon="/static/images/data.png">
		</u-empty>
		<view class="u-demo-block">
			<view v-for="(item, index) in dataMap" :key="index">
				<u-row gutter="24" :key="index" v-if='index%2 == 0'>
					<u-col span="6">
						<u-row>
							<u-col offset="1" span="1">
								<image style="margin: 40px 0 0 2px;width: 35px;height: 35px;" class="u-cell-icon"
									:src="getIcon(item.icon)" mode="scaleToFill"></image>
							</u-col>
							<u-col span="9">
								<text class="u-demo-block__title"
									style="margin: 35px 0 0 30px;font-size: 17px;">{{item.categoryName}}</text>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<text style="margin: 30px 0 0 20px;font-size: 15px;">总数</text>
							</u-col>
							<u-col span="4">
								<text style="margin: 30px 0 0 20px;font-size: 15px;">正常</text>
							</u-col span="4">
							<u-col>
								<text style="margin: 30px 0 0 20px;font-size: 15px;">异常</text>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<text style="margin: 30px 0 0 20px;font-size: 15px;">{{item.assetTotal}}</text>
							</u-col>
							<u-col span="4">
								<text
									style="margin: 30px 0 0 20px;font-size: 15px;color: cornflowerblue;">{{item.noalarmAssetNum}}</text>
							</u-col>
							<u-col span="4">
								<text
									style="margin: 30px 0 0 20px;font-size: 15px;color: chocolate;">{{item.alarmAssetNum}}</text>
							</u-col>
						</u-row>
					</u-col>
					<u-col span="6" v-if="index+1 < dataMap.length">
						<u-row>
							<u-col offset="1" span="1">
								<image style="margin: 40px 0 0 2px;width: 35px;height: 35px;" class="u-cell-icon"
									:src="getIcon(dataMap[index+1].icon)" mode="scaleToFill"></image>
							</u-col>
							<u-col span="9">
								<text class="u-demo-block__title"
									style="margin: 35px 0 0 30px;font-size: 17px;">{{dataMap[index+1].categoryName}}</text>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<text style="margin: 30px 0 0 20px;font-size: 15px;">总数</text>
							</u-col>
							<u-col span="4">
								<text style="margin: 30px 0 0 20px;font-size: 15px;">正常</text>
							</u-col span="4">
							<u-col>
								<text style="margin: 30px 0 0 20px;font-size: 15px;">异常</text>
							</u-col>
						</u-row>
						<u-row>
							<u-col span="4">
								<text
									style="margin: 30px 0 0 20px;font-size: 15px;">{{dataMap[index+1].assetTotal}}</text>
							</u-col>
							<u-col span="4">
								<text
									style="margin: 30px 0 0 20px;font-size: 15px;color: cornflowerblue;">{{dataMap[index+1].noalarmAssetNum}}</text>
							</u-col>
							<u-col span="4">
								<text
									style="margin: 30px 0 0 20px;font-size: 15px;color: chocolate;">{{dataMap[index+1].alarmAssetNum}}</text>
							</u-col>
						</u-row>
					</u-col>
				</u-row>
			</view>
		</view>
		<u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" :mode="popupData.mode" :show="show"
			:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
			:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="show = false">
			<view class="u-popup-slot" :style="{
				width: '200px',
				overflow: auto,
				marginTop: '20px',marginLeft: '30px'
			}">
				<view>
					<u-button text="确定" size="normal" type="success" plain
						customStyle="width: 60%;height: 30px;margin:30px 0 10px 25px" @click="openPopup">
					</u-button>
					<u-checkbox-group name="allCheck" @change="allSelect">
						<u-checkbox customStyle="margin:20px 0 0 0" :checked="!allSelectFlag" shape="circle" label="全选">
						</u-checkbox>
					</u-checkbox-group>
					<u-line customStyle="margin:10px 0 10px -15px"></u-line>
				</view>
				<scroll-view scroll-y="true" style="height: 900rpx;">
					<view class="u-demo-block" v-for="(item, i) in treeDataMap">
						<text class="u-demo-block__title">{{item.label}}</text>
						<view class="u-demo-block__content">
							<view class="u-page__checkbox-item">
								<u-checkbox-group v-model="checkboxValue[i]" placement="column"
									@change="checkboxChange">
									<u-checkbox :customStyle="{marginBottom: '8px'}" shape="circle"
										v-for="(obj, index) in item.options" :key="index" :label="obj.label"
										:checked="item.isSelect" :name="obj.value">
									</u-checkbox>
								</u-checkbox-group>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dataShow: false,
				show: false,
				allSelectFlag: true,
				checkboxValue: [],
				popupData: {
					overlay: true,
					mode: 'right',
					borderRadius: '',
					round: 10,
					closeable: true,
					closeOnClickOverlay: true
				},
				dataMap: [],
				origDataMap: [], //原始集合
				treeDataMap: [],
			}
		},
		onLoad() {
			this.$H.checkLoginAndJumpStart();//检查登录状态
			if(this.$H.isLogin){
				this.getResourceSelectList();
			}
		},
		computed: {
			getIcon() {
				return path => {
					return '/static/images/icon/physical-server.svg';
					// return '/static/asset/' + path + '.png';
				}
			},
		},
		methods: {
			checkboxChange(n) {
				let current = this
				console.log('change', JSON.stringify(n));
			},
			allSelect(e, data) {
				let current = this
				if (current.allSelectFlag) {
					current.checkboxValue = [];
					this.treeDataMap.forEach(function(item, index) {
						let value = [];
						item.options.forEach(function(obj, i) {
							value.push(obj.value);
						});
						current.checkboxValue.push(value);
					});
					current.allSelectFlag = false;
				} else {
					current.checkboxValue = [];
					current.allSelectFlag = true;
				}
			},
			openPopup() {
				uni.$u.sleep().then(() => {
					this.show = !this.show
				})
				if (this.show) {
					this.queryCategoryCount();
				}
			},
			getResourceSelectList() {
				let current = this
				current.$H.get('lbeam-core/webview/asset-model-category/getSimpleSelectVo')
					.then(
						res => {
							if (res.status == "0") {
								current.treeDataMap = res.data;
								current.allSelect();
								current.queryCategoryCount();
							}
						})
			},
			queryCategoryCount() {
				let current = this
				let param = [];
				current.checkboxValue.forEach(function(item, index) {
					item.forEach(function(obj, index) {
						param.push(obj)
					});
				});
				console.log("#######", JSON.stringify(param));
				// current.$H.post('http://10.1.22.122:8080/rest/lbeam-core/asset/asset-alarm-agg/getAssetCountNum', param,
				current.$H.post('lbeam-core/asset/asset-alarm-agg/getAssetCountNum', param,
					true).then(
					res => {
						if (res.status == "0") {
							console.log("queryCategoryCount", JSON.stringify(res));
							current.dataMap = res.data;
							current.origDataMap = res.data;
							if (res.data.length < 1) {
								current.dataShow = true; //空数据
							} else {
								current.dataShow = false;
							}
						}
					})
			},
			// queryCategoryCount2() {
			// 	let current = this;
			// 	current.$H.get('lbeam-core/asset/asset-alarm-agg/getAssetCountNum').then(res => {
			// 		if (res.status == "0") {
			// 			this.dataMap = res.data.rows;
			// 			this.origDataMap = res.data.rows;
			// 		}
			// 		console.log("###", res);
			// 	});
			// },
		}
	}
</script>

<style>

</style>
