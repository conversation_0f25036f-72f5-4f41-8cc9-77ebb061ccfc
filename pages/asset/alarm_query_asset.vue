<template>
	<view class="query-asset">
		<uni-search-bar class="asset-searchbar" style="margin-top: -10px;background-color: #fff;" always
			placeholder="告警标题或IP地址或告警级别" clear-button="none" cancel-button="none" @confirm="search"
			v-model="searchValue" @clear="clear">
		</uni-search-bar>
		<scroll-view v-if="currentType != '告警查询'" ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll"
			scroll-with-animation :scroll-left="scrollLeft">
			<view class="tab-bar">
				<!-- :class="{ 'active': activeIndex == index }" -->
				<view :ref="`tabItem${index}`" v-for="(item, index) in tabs" :key="index" class="tab-item"
					@click="switchTab(index)">
					{{ item.title }}
				</view>
				<!-- 底部滑动条 -->
				<view ref="tabLine" class="tab-line" :style="lineStyle"></view>
			</view>
		</scroll-view>
		<!-- <view style="margin-right: 20px; margin-bottom: 5px; margin-top: -5px;text-align: right; font-size: .8em;">
			<text>共{{totalAsset}}个资产</text>
		</view> -->
		<swiper v-if="currentType != '告警查询'" :current="activeIndex" @change="onSwiperChange" :duration="300">
			<swiper-item v-for="(item, index) in tabs" :key="index">
				<view class="content">
					<scroll-view class="asset-content" :class="{ 'asset-content-bottom': !registerMode }" scroll-y
						style="overflow: auto;" @scrolltolower="scrollBottom()">
						<uni-section v-for="(assetReocrd, i) in assetReocrds_alarm" :key="i" title=""
							class="asset-item">
							<!-- <template #decoration>
								<view style="display: flex; align-items: center; width: 100%;" @click="showAssetDetail(assetReocrd)">
									<view :class="{'unconfirm_asset_bg': assetReocrd.confirmStatus != '确认', 'confirmed_asset_bg': assetReocrd.confirmStatus == '确认'}" style="width: 64px; height: 20px;display: flex;align-items: center;justify-content: center;">
										<image :src="assetReocrd.confirmStatus != '确认' ? unConfirmAssetImg : confirmedAssetImg" style="width: 40px; height: 12px;"></image>
									</view>
									<view style="margin-left: 20px;font-size: 12pt; font-weight: bold;">{{assetReocrd.name || assetReocrd.ipAddress || '-'}}</view>
								</view>
								<uni-icons type="compose" color="#007aff" @click="toEditAsset(assetReocrd)"></uni-icons>
							</template> -->

							<view>
								<uni-row class="asset-column" style="color: #3C5176; font-size: .98em;">
									<uni-col :span="12" style="width: 100px;">
										<view class="label">告警发生时间：</view>
									</uni-col>
									<uni-col :span="12" style="width: calc(100% - 100px);">
										<view class="value" style="float: left;">{{ assetReocrd.updateTime || '暂未更新' }}
										</view>
										<!-- <view style="float: right;">
											<uni-icons type="compose" color="#007aff"
												@click="toEditAsset(assetReocrd)"></uni-icons>
										</view> -->
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<!-- 标题 -->
							<view class="asset-title" @click="showAssetDetail(assetReocrd)">
								<uni-col :span="9" style="width: 80px;">
									<view class="label">告警标题：</view>
								</uni-col>
								<uni-col :span="15" style="width: calc(100% - 140px);">
									<view class="value">{{ assetReocrd.categoryName }}</view>
								</uni-col>

							</view>

							<view @click="showAssetDetail(assetReocrd)">
								<uni-row class="asset-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">告警对象：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ assetReocrd.ipAddress }}</view>
									</uni-col>
								</uni-row>
								<uni-row class="asset-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">告警正文：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ assetReocrd.zoneName }}</view>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="showAssetDetail(assetReocrd)">
								<uni-row class="asset-column">
									<uni-col :span="19">
										<!-- <view class="online-status" :class="{'online-status-on': assetReocrd.onlineStatus == '在线'}"></view> -->
										<uni-tag :type="assetReocrd.onlineStatus == '在线' ? 'success' : 'error'"
											:text="assetReocrd.onlineStatus || '离线'"></uni-tag>
										<!-- <view style="font-weight: bold; font-size: 14pt;margin-left: 8.5pt;">{{ assetReocrd.name
									||
									assetReocrd.ipAddress ||
									'-' }}</view> -->
									</uni-col>
									<uni-col style="width: 60px;text-align: right;">
										<view style="color: #1CB91C;"
											:style="{ color: assetReocrd.confirmStatus == '已确认' ? '#1CB91C' : '#F6605A' }">
											{{ assetReocrd.confirmStatus }}</view>
									</uni-col>
								</uni-row>
							</view>

						</uni-section>

						<template v-if="assetReocrds_alarm.length == 0">
							<view class="empty-text">暂无数据</view>
						</template>

					</scroll-view>
				</view>
			</swiper-item>
		</swiper>
		<view style="background-color: #fff;padding: 16rpx;background-color: #fff;display: flex;">
			<uni-data-select style="margin: 0 16rpx;" v-model="searchForm.alarmSeverity"
				:localdata="rangSearchForm.alarmSeverity" @change="change" placeholder="告警级别"></uni-data-select>
			<uni-data-select v-model="searchForm.disposalState" :localdata="rangSearchForm.disposalState"
				@change="change" placeholder="处置状态"></uni-data-select>
			<uni-data-select style="margin: 0 16rpx;" v-model="searchForm.alarmOccurrenceTime"
				:localdata="rangSearchForm.alarmOccurrenceTime" @change="change" placeholder="告警发生时间"></uni-data-select>
		</view>
		<view style="background-color: #fff;padding-left: 30rpx;font-size: 30rpx;color: #666;">累计1条告警，未处置1条告警</view>
		<view v-if="currentType == '告警查询'" class="content" style="padding-top: 6rpx;">
			<uni-section v-for="(assetReocrd, i) in assetReocrds_alarm" :key="i" title="" class="asset-item">
				<view>
					<uni-row class="asset-column" style="color: #3C5176; font-size: .98em;">
						<uni-col :span="12" style="width: 100px;">
							<view class="label">告警发生时间：</view>
						</uni-col>
						<uni-col :span="12" style="width: calc(100% - 100px);">
							<view class="value" style="float: left;">{{ assetReocrd.updateTime || '暂未更新' }}
							</view>
							<!-- <view style="float: right;">
								<uni-icons type="compose" color="#007aff" @click="toEditAsset(assetReocrd)"></uni-icons>
							</view> -->
						</uni-col>
					</uni-row>
				</view>

				<view class="split-line"></view>

				<!-- 标题 -->
				<view class="asset-title" @click="showAssetDetail(assetReocrd)">
					<uni-col :span="9" style="width: 80px;">
						<view class="label">告警标题：</view>
					</uni-col>
					<uni-col :span="15" style="width: calc(100% - 140px);">
						<view class="value">{{ assetReocrd.categoryName }}</view>
					</uni-col>

				</view>

				<view @click="showAssetDetail(assetReocrd)">
					<uni-row class="asset-column">
						<uni-col :span="9" style="width: 80px;color: #10172A;">
							<view class="label">告警对象：</view>
						</uni-col>
						<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
							<view class="value">{{ assetReocrd.ipAddress }}</view>
						</uni-col>
					</uni-row>
					<uni-row class="asset-column">
						<uni-col :span="9" style="width: 80px;color: #10172A;">
							<view class="label">告警正文：</view>
						</uni-col>
						<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
							<view class="value">{{ assetReocrd.zoneName }}</view>
						</uni-col>
					</uni-row>
				</view>

				<view class="split-line"></view>

				<view @click="showAssetDetail(assetReocrd)">
					<uni-row class="asset-column">
						<uni-col :span="19">
							<!-- <view class="online-status" :class="{'online-status-on': assetReocrd.onlineStatus == '在线'}"></view> -->
							<uni-tag :type="assetReocrd.onlineStatus == '在线' ? 'success' : 'error'"
								:text="assetReocrd.onlineStatus || '离线'"></uni-tag>
							<!-- <view style="font-weight: bold; font-size: 14pt;margin-left: 8.5pt;">{{ assetReocrd.name
									||
									assetReocrd.ipAddress ||
									'-' }}</view> -->
						</uni-col>
						<uni-col style="width: 60px;text-align: right;">
							<view style="color: #1CB91C;"
								:style="{ color: assetReocrd.confirmStatus == '已确认' ? '#1CB91C' : '#F6605A' }">
								{{ assetReocrd.confirmStatus }}</view>
						</uni-col>
					</uni-row>
				</view>

			</uni-section>

			<template v-if="assetReocrds_alarm.length == 0">
				<view class="empty-text">暂无数据</view>
			</template>
		</view>

		<!-- <view v-if="registerMode" style="position: fixed;bottom: 0px; width: 100%;"> -->
		<!-- 			<view style="margin-right: 10px;display: flex; align-items: center;display: flex;align-items: center;justify-content: space-between;">
				<uni-pagination style="flex: 1" :show-icon="true" :current="pageNum" :page-size="pageSize" :total="totalAsset" @change="onPageChange"/>
				<view style="flex: 1; text-align: right;font-size: .8em;" >共{{totalAsset}}个资产</view>
			</view> -->
		<!-- <button type="primary" @click="showRegisterAssetTypes" style="margin-top: 20px;">快速登记</button>
		</view> -->

		<!-- 弹窗设计 -->
		<!-- <uni-popup ref="popup" background-color="#fff">
			<uni-section title="选择资产类型">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 30px); overflow: auto;">
						<uni-col :span="8" v-for="(assetType, itemIndex) in assetTypes" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<view class="asset-type"
									:class="{ 'asset-type-selected': selectAssetType == assetType.value }"
									@click="selectAssetType = assetType.value">
									<text :title="assetType.label">{{ assetType.label }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
			<button type="primary" style="position: absolute;bottom: 0;width: 100%;"
				@click="toRegisterAsset">确定</button>
		</uni-popup> -->
	</view>
</template>

<script>
import asset from "./asset.js"
import { queryTableDataByCategory } from "./api/index.js"
export default {
	mixins: [asset],
	props: {
		isShowTabs: {
			type: Boolean,
            default: true
		}
	},
	data() {
		return {
			searchForm: {
				alarmSeverity: "",
				disposalState: "",
				alarmOccurrenceTime: ""
			},
			rangSearchForm: {
				alarmSeverity: [
					{ value: 0, text: "紧急" },
					{ value: 1, text: "重要" },
					{ value: 2, text: "轻微" },
				],
				disposalState: [
					{ value: 0, text: "未处置" },
					{ value: 1, text: "已处置" },
					{ value: 2, text: "流转中" },
				],
				alarmOccurrenceTime: [
					{ value: 0, text: "近一月" },
					{ value: 1, text: "近半年" },
					{ value: 2, text: "近一年" },
				]
			},
			tabs: [
				{ title: '今日告警', content: '第一页内容' },
				{ title: '昨日告警', content: '第二页内容' },
				{ title: '最近7天', content: '第三页内容' },
				{ title: '最近30天', content: '第四页内容' },
			],
			activeIndex: 0,     // 当前选中索引
			itemWidth: 0,       // 单个 Tab 的宽度
			lineWidth: 0,
			scrollLeft: 0,     // 滚动条位置
			lineLeft: 0,
			searchValue: "",
			// unConfirmAssetImg: "/static/asset/unconfirm_asset.png", 
			// confirmedAssetImg: "/static/asset/confirmed_asset.png", 

			popType: "bottom",
			selectAssetType: null,
			assetTypes: [],

			pageNum: 1,
			pageSize: 20,
			totalAsset: 0,
			assetReocrds_alarm: [
				{
					gutter: '',
					"instanceId": "654",
					"onlineStatus": "异常",
					"ipAddress": "**********",
					"name": "9",
					"confirmStatus": "已确认",
					"updateTime": "2024-11-01 16:10:56",
					"zoneName": null,
					"categoryName": "服务器",
					"categoryId": 125
				},
			],

			registerMode: false,

			currentType: ""
		}
	},
	onLoad(page) {
		// if (page.type) {
		// 	this.registerMode = true;
		// 	uni.setNavigationBarTitle({
		// 		title: "资产登记"
		// 	});
		// }
		if (page.type == '告警查询') {
			uni.setNavigationBarTitle({
				title: "告警查询"
			});
			this.currentType = '告警查询'
		} else {
			this.currentType = ''
		}
	},
	computed: {
		// 底部滑动条样式
		lineStyle() {
			return {
				width: `${this.lineWidth}px`, // 改用 lineWidth
				transform: `translateX(${this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2}px)`, // 居中计算
				transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
				// #ifdef APP-PLUS
				width: '166rpx',
				transform: `translateX(${this.activeIndex * 166}rpx)`,
				marginLeft: '6rpx',
				margin: ' 0 auto',
				// #endif
			}
		},
		totalPage() {
			let pageSize = this.pageSize;
			let total = this.totalAsset;
			let n = total % pageSize;
			let totalPage = n == 0 ? total / pageSize : 1 + (total - n) / pageSize;
			return totalPage == 0 ? 1 : totalPage;
		}
	},
	mounted() {
		this.queryAssetCategorys((assetTypes) => {
			this.assetTypes = assetTypes;
			this.selectAssetType = assetTypes[0] ? assetTypes[0].value : null;
		});
		this.loadAssetPropertyGroups();
		this.refresh();
		// 获取 Tab 项宽度（根据实际样式调整）
		this.calcTabWidth()
	},
	onBackPress() {
		uni.hideLoading();
	},
	methods: {
		// 统一计算逻辑（跨平台）
		calcTabWidth() {
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom')
			console.log(this.$refs[`tabItem${this.activeIndex}`][0])
			dom.getComponentRect(this.$refs[`tabItem${this.activeIndex}`][0], res => {
				console.log('APP端元素尺寸:', res)
				if (res?.size?.width) {
					this.itemWidth = res.size.width
					this.lineWidth = res.size.width * 0.8
					// 测试用提示
					// uni.showToast({ title: `宽度:${this.itemWidth}`, icon: 'none' })
				} else {
					// uni.showToast({ title: '获取宽度失败', icon: 'none' })
				}
			})
			// #endif

			// #ifndef APP-PLUS
			// 非原生环境通用逻辑
			const query = uni.createSelectorQuery().in(this)
			query.select('.tab-item').boundingClientRect(res => {
				if (res) {
					this.itemWidth = res.width
					this.lineWidth = res.width * 0.8
				}
			}).exec()
			// #endif
		},

		// 调整滚动位置（跨平台兼容）
		adjustScrollPosition() {
			const systemInfo = uni.getSystemInfoSync();
			console.log(systemInfo)
			let offset = this.activeIndex * this.itemWidth - systemInfo.windowWidth / 2 + this.itemWidth / 2
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom');
			const el = this.$refs.tabLine
			dom.scrollToElement(el, {
				offset: 0
			})
			// #endif
			// #ifdef APP-PLUS
			// 原生环境增加安全偏移
			offset = Math.max(0, offset - 8)
			// 原生滚动控制
			this.$nextTick(() => {
				this.$refs.tabScroll.setScrollLeft({
					scrollLeft: offset,
					duration: 300
				})
			})
			// #else
			// 非原生环境直接赋值
			this.scrollLeft = Math.max(0, offset)
			// #endif

			// 滑动条位置计算（跨平台通用）
			this.lineLeft = this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2
		},

		// 点击切换 Tab
		switchTab(index) {
			this.activeIndex = index
			this.adjustScrollPosition()
		},

		// 滑动切换回调
		onSwiperChange(e) {
			this.activeIndex = e.detail.current
			this.adjustScrollPosition()
		},
		refresh() {
			this.queryPageAssets();
			this.closePop();
		},
		scrollTop() {
			// if(this.pageNum > 1) {
			// 	--this.pageNum;
			// 	this.queryPageAssets();
			// }
		},
		scrollBottom() {
			if (this.pageNum < this.totalPage) {
				++this.pageNum;
				this.queryPageAssets(true);
			} else {
				uni.showToast({
					title: "没有更多数据了"
				})
			}
		},
		// onPageChange({type, current}) {
		// 	this.pageNum = current;
		// 	this.queryPageAssets();
		// },
		clear() {
			this.searchValue = null;
			this.search();
		},
		search() {
			this.pageNum = 1;
			this.$nextTick(this.queryPageAssets);
		},
		input() {
		},
		queryPageAssets(appendFlag) {
			let params = {
				queryValue: this.searchValue,
				pageSize: this.pageSize,
				pageNum: this.pageNum
			}

			uni.showLoading({
				title: "加载中...",
				icon: "loading"
			})

			// this.assetReocrds_alarm = [
			// 	{
			// 		gutter: '',
			// 		"instanceId": "654",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "**********",
			// 		"name": "9",
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-11-01 16:10:56",
			// 		"zoneName": null,
			// 		"categoryName": "服务器",
			// 		"categoryId": 125
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "19",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": null,
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 18:17:46",
			// 		"zoneName": "测试组织i7oh5",
			// 		"categoryName": "政务外网接入单位",
			// 		"categoryId": 137
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "20",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "************",
			// 		"name": "测试业务系统",
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 18:17:46",
			// 		"zoneName": "研发部",
			// 		"categoryName": "服务器",
			// 		"categoryId": 125
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "18",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "*******",
			// 		"name": "资产名称测试",
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 17:14:57",
			// 		"zoneName": "研发部",
			// 		"categoryName": "网络设备",
			// 		"categoryId": 129
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "13",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": null,
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 17:14:57",
			// 		"zoneName": "测试组织p5ndc",
			// 		"categoryName": "政务外网接入单位",
			// 		"categoryId": 137
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "12",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": null,
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 17:14:57",
			// 		"zoneName": "测试组织i7oh5",
			// 		"categoryName": "政务外网接入单位",
			// 		"categoryId": 137
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "14",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": null,
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-06 17:14:57",
			// 		"zoneName": "测试组织zon10",
			// 		"categoryName": "政务外网接入单位",
			// 		"categoryId": 137
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "672",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "********",
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-10 11:33:19",
			// 		"zoneName": "测试组织vk7xr",
			// 		"categoryName": "录像机",
			// 		"categoryId": 15
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "675",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "1.1",
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2024-12-10 12:26:10",
			// 		"zoneName": "",
			// 		"categoryName": "录像机",
			// 		"categoryId": 15
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "673",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "********",
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2025-03-14 10:01:03",
			// 		"zoneName": "测试组织vk7xr",
			// 		"categoryName": "录像机",
			// 		"categoryId": 15
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "674",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "********",
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2025-03-14 10:01:03",
			// 		"zoneName": "测试组织vk7xr",
			// 		"categoryName": "录像机",
			// 		"categoryId": 15
			// 	},
			// 	{
			// 		gutter: '',
			// 		"instanceId": "676",
			// 		"onlineStatus": "异常",
			// 		"ipAddress": "1.1",
			// 		"name": null,
			// 		"confirmStatus": "已确认",
			// 		"updateTime": "2025-03-14 10:01:03",
			// 		"zoneName": "",
			// 		"categoryName": "录像机",
			// 		"categoryId": 15
			// 	}
			// ];
			uni.hideLoading();
			// queryTableDataByCategory(params).then(res => {
			// 	let { total, list } = res.data || {};
			// 	this.totalAsset = total || 0;
			// 	let records = list || [];
			// 	if (appendFlag) {
			// 		this.assetReocrds_alarm.push(...records);
			// 	} else {
			// 		console.log(records);
			// 		this.assetReocrds_alarm = [
			// 			{
			// 				"instanceId": "654",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "**********",
			// 				"name": "9",
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-11-01 16:10:56",
			// 				"zoneName": null,
			// 				"categoryName": "服务器",
			// 				"categoryId": 125
			// 			},
			// 			{
			// 				"instanceId": "19",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": null,
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 18:17:46",
			// 				"zoneName": "测试组织i7oh5",
			// 				"categoryName": "政务外网接入单位",
			// 				"categoryId": 137
			// 			},
			// 			{
			// 				"instanceId": "20",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "************",
			// 				"name": "测试业务系统",
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 18:17:46",
			// 				"zoneName": "研发部",
			// 				"categoryName": "服务器",
			// 				"categoryId": 125
			// 			},
			// 			{
			// 				"instanceId": "18",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "*******",
			// 				"name": "资产名称测试",
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 17:14:57",
			// 				"zoneName": "研发部",
			// 				"categoryName": "网络设备",
			// 				"categoryId": 129
			// 			},
			// 			{
			// 				"instanceId": "13",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": null,
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 17:14:57",
			// 				"zoneName": "测试组织p5ndc",
			// 				"categoryName": "政务外网接入单位",
			// 				"categoryId": 137
			// 			},
			// 			{
			// 				"instanceId": "12",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": null,
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 17:14:57",
			// 				"zoneName": "测试组织i7oh5",
			// 				"categoryName": "政务外网接入单位",
			// 				"categoryId": 137
			// 			},
			// 			{
			// 				"instanceId": "14",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": null,
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-06 17:14:57",
			// 				"zoneName": "测试组织zon10",
			// 				"categoryName": "政务外网接入单位",
			// 				"categoryId": 137
			// 			},
			// 			{
			// 				"instanceId": "672",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "********",
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-10 11:33:19",
			// 				"zoneName": "测试组织vk7xr",
			// 				"categoryName": "录像机",
			// 				"categoryId": 15
			// 			},
			// 			{
			// 				"instanceId": "675",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "1.1",
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2024-12-10 12:26:10",
			// 				"zoneName": "",
			// 				"categoryName": "录像机",
			// 				"categoryId": 15
			// 			},
			// 			{
			// 				"instanceId": "673",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "********",
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2025-03-14 10:01:03",
			// 				"zoneName": "测试组织vk7xr",
			// 				"categoryName": "录像机",
			// 				"categoryId": 15
			// 			},
			// 			{
			// 				"instanceId": "674",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "********",
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2025-03-14 10:01:03",
			// 				"zoneName": "测试组织vk7xr",
			// 				"categoryName": "录像机",
			// 				"categoryId": 15
			// 			},
			// 			{
			// 				"instanceId": "676",
			// 				"onlineStatus": "异常",
			// 				"ipAddress": "1.1",
			// 				"name": null,
			// 				"confirmStatus": "已确认",
			// 				"updateTime": "2025-03-14 10:01:03",
			// 				"zoneName": "",
			// 				"categoryName": "录像机",
			// 				"categoryId": 15
			// 			}
			// 		];
			// 	}
			// 	console.log(" this.totalAsset ", this.totalAsset);
			// }).catch(err => { }).then(() => {
			// 	uni.hideLoading();
			// });
		},
		showRegisterAssetTypes() {
			this.$refs.popup.open(this.popType);
		},
		closePop() {
			// this.$refs.popup.close();
		},
		toRegisterAsset() {
			let selectAssetType = this.selectAssetType;
			let assetType = this.assetTypes.find(assetType => assetType.value == selectAssetType);
			uni.navigateTo({
				url: "/pages/asset/register_asset?assetType=" + selectAssetType + "&assetTypeLabel=" + assetType.label
			})
		},
		toEditAsset(assetRecord) {
			console.log("================ edit ... ");
			let assetModel = {
				type: assetRecord.categoryId,
				record: assetRecord
			}
			this.setCurrent(assetModel);
			// 跳转至资产编辑
			uni.navigateTo({
				url: "/pages/asset/edit_asset"
			})
		},
		showAssetDetail(assetRecord) {
			// let assetModel = {
			// 	type: assetRecord.categoryId,
			// 	record: assetRecord
			// }
			// this.setCurrent(assetModel);
			// 跳转至详情
			uni.navigateTo({
				url: "/pages/list/new_alarm_details"
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.query-asset {
	font-size: 13pt;
	overflow: hidden;

	.asset-searchbar {
		:deep(.uni-searchbar__box) {
			justify-content: unset !important;
		}
	}

	.asset-content {
		/* #ifndef H5 */
		height: calc(100vh - 140px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 200px);

		/* #endif */
		&-bottom {
			/* #ifndef H5 */
			height: calc(100vh - 80px);
			/* #endif */
			/* #ifdef H5 */
			height: calc(100vh - 140px);
			/* #endif */
		}

		.empty-text {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: gray;
		}
	}

	// .unconfirm_asset_bg {
	// 	background-image: url('/static/asset/unconfirm_asset_bg.png'); 
	// }
	// .confirmed_asset_bg {
	// 	background-image: url('/static/asset/confirmed_asset_bg.png'); 
	// }

	.asset-item {
		margin: 5px 10px 10px;
		padding: 10px 20px;
		font-size: 13pt;
		border: 2rpx solid #ddd;
		border-radius: 16rpx;

		.split-line {
			height: 1px;
			background-color: #E3E8F0;
			transform: scaleY(.7);
			margin: 8px 0;
		}

		:deep(.uni-section-header) {
			display: none;
		}

		.asset-title {
			display: flex;
			align-items: center;
			margin-bottom: 8pt;
			// .online-status {
			// 	background-color: gray;
			// 	width: 11.5pt; 
			// 	height: 11.5pt;
			// 	&-on {
			// 		background-color: #1CB91C;
			// 	}
			// }
		}

		.asset-column {
			line-height: 25px;

			.label {
				//margin-left: 10px;
			}
		}
	}

	.popup-content {
		max-height: 80vh;
		padding-bottom: 50px;
		position: relative;

		.asset-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;

			&-selected {
				background-color: #3190F9;
				color: white;
			}

			// text {
			// 	overflow: hidden;
			// 	white-space: nowrap;
			// 	text-overflow: ellipsis;
			// }
		}
	}
}

/* Tab 栏样式 */
.tab-scroll {
	width: 100%;
	height: 44px;
	background: #fff;
	border-bottom: 1px solid #eee;

}

.tab-bar {
	position: relative;
	height: 100%;
	white-space: nowrap;
}

.tab-item {
	display: inline-block;
	height: 44px;
	line-height: 44px;
	padding: 0 26rpx;
	font-size: 29rpx;
	color: #666;
	transition: color 0.3s;
}

.tab-item.active {
	color: #007AFF;
	font-weight: bold;
}

/* 底部滑动条 */
.tab-line {
	position: absolute;
	bottom: 0;
	height: 3px;
	background: #007AFF;
	transition: transform 0.3s ease;
}

/* 内容区域 */
swiper {
	flex: 1;
	height: calc(100vh - 44px);
}

.content {
	height: 166%;
	// padding: 20px;
	background: #fff;
}

/* 隐藏滚动条（全平台通用） */
// .tab-scroll {
// 	overflow: hidden !important;
// }

/* 针对 H5 的隐藏方式 */
.tab-scroll ::-webkit-scrollbar {
	display: none !important;
	width: 0 !important;
	height: 0 !important;
	color: transparent !important;
}
</style>