<template>
	<view class="confirm-asset">
		<uni-search-bar class="asset-searchbar" style="margin-top: -10px;" always placeholder="资产名称及IP地址或单位和资产类型"
			clear-button="none" cancel-button="none" @confirm="search" v-model="searchValue" @clear="clear">
		</uni-search-bar>
		<uni-row style="margin-right: 20px; margin-bottom: 5px; margin-top: -5px; padding-left: 10px;font-size: .8em;">
			<uni-col :span="12">
				<button type="default" size="mini" @click.stop="confirmInstance(batchConfirmation,'bathConfirmation')">批量确认</button>
				<!-- <button type="primary" size="mini">确定</button> -->
			</uni-col>
			<uni-col :span="12" style="text-align: right; ">
				<text>共{{totalAsset}}个资产</text>
			</uni-col>
		</uni-row>
		<scroll-view class="asset-content" :class="{'asset-content-bottom': !registerMode}" scroll-y
			style="overflow: auto;" @scrolltolower="scrollBottom()">

			<checkbox-group @change="checkboxChange">

				<uni-section v-for="(assetReocrd, i) in assetReocrds" :key="assetReocrd.instanceId" title=""
					class="asset-item">
					<!-- 				<template #decoration>
					<view style="display: flex; align-items: center; width: 100%;" @click="showAssetDetail(assetReocrd)">
						<view :class="{'unconfirm_asset_bg': assetReocrd.confirmStatus != '确认', 'confirmed_asset_bg': assetReocrd.confirmStatus == '确认'}" style="width: 64px; height: 20px;display: flex;align-items: center;justify-content: center;">
							<image :src="assetReocrd.confirmStatus != '确认' ? unConfirmAssetImg : confirmedAssetImg" style="width: 40px; height: 12px;"></image>
						</view>
						<view style="margin-left: 20px;font-size: 12pt; font-weight: bold;">{{assetReocrd.name || assetReocrd.ipAddress || '-'}}</view>
					</view>
					<uni-icons type="compose" color="#007aff" @click="toEditAsset(assetReocrd)"></uni-icons>
				</template> -->

					<view>
						<uni-row class="asset-column" style="color: #3C5176; font-size: .98em;">
							<uni-col :span="9" style="width: 80px;">
								<view class="label">更新时间：</view>
							</uni-col>
							<uni-col :span="15" style="width: calc(100% - 80px);">
								<view class="value" style="float: left;">{{assetReocrd.updateTime || '暂未更新'}}</view>
								<view style="float: right;">
									<checkbox :value="assetReocrd.instanceId"  color="#FFCC33" style="transform:scale(0.7)" />
									<!-- <uni-icons type="checkbox" color="#007aff"
										@click="toChecked(assetReocrd)"></uni-icons> -->
								</view>
							</uni-col>
						</uni-row>
					</view>

					<view class="split-line"></view>

					<!-- 标题 -->
					<view class="asset-title" @click="showAssetDetail(assetReocrd)">
						<!-- <view class="online-status" :class="{'online-status-on': assetReocrd.onlineStatus == '在线'}"></view> -->
						<uni-tag :type="assetReocrd.onlineStatus == '在线' ? 'success' : 'error'"
							:text="assetReocrd.onlineStatus || '离线'"></uni-tag>
						<view style="font-weight: bold; font-size: 14pt;margin-left: 8.5pt;">
							{{assetReocrd.name || assetReocrd.ipAddress || '-'}}
						</view>
					</view>

					<view @click="showAssetDetail(assetReocrd)">
						<uni-row class="asset-column">
							<uni-col :span="9" style="width: 80px;color: #10172A;">
								<view class="label">设备地址：</view>
							</uni-col>
							<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
								<view class="value">{{assetReocrd.ipAddress}}</view>
							</uni-col>
						</uni-row>
						<uni-row class="asset-column">
							<uni-col :span="9" style="width: 80px;color: #10172A;">
								<view class="label">责任单位：</view>
							</uni-col>
							<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
								<view class="value">{{assetReocrd.zoneName}}</view>
							</uni-col>
						</uni-row>
					</view>

					<view class="split-line"></view>

					<view @click="showAssetDetail(assetReocrd)">
						<uni-row class="asset-column">
							<uni-col :span="9" style="width: 80px;">
								<view class="label">资产类型：</view>
							</uni-col>
							<uni-col :span="15" style="width: calc(100% - 140px);">
								<view class="value">{{assetReocrd.categoryName || '-'}}</view>
							</uni-col>

							<uni-col style="width: 60px;text-align: right;">
								<view class="confirm-btn" @click.stop="confirmInstance(assetReocrd)">确认</view>
							</uni-col>
						</uni-row>
					</view>

				</uni-section>
			</checkbox-group>
			<template v-if="assetReocrds.length == 0">
				<view class="empty-text">暂无数据</view>
			</template>

		</scroll-view>


		<!-- 弹窗设计 -->
		<uni-popup ref="popup" background-color="#fff">
			<uni-section title="确认结果">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>

				</scroll-view>
			</uni-section>
		</uni-popup>
	</view>
</template>

<script>
	import asset from "./asset.js"
	import {
		queryTableDataByCategory,
		confirmInstance
	} from "./api/index.js"
	export default {
		mixins: [asset],
		data() {
			return {
				batchConfirmation:{
					instanceId:[]
				},
				searchValue: "",
				// unConfirmAssetImg: "/static/asset/unconfirm_asset.png", 
				// confirmedAssetImg: "/static/asset/confirmed_asset.png", 
				popType: "bottom",
				selectAssetType: null,
				assetTypes: [],

				pageNum: 1,
				pageSize: 20,
				totalAsset: 0,
				assetReocrds: [],

				registerMode: false
			}
		},
		onLoad(page) {},
		computed: {
			totalPage() {
				let pageSize = this.pageSize;
				let total = this.totalAsset;
				let n = total % pageSize;
				let totalPage = n == 0 ? total / pageSize : 1 + (total - n) / pageSize;
				return totalPage == 0 ? 1 : totalPage;
			}
		},
		mounted() {
			this.queryAssetCategorys((assetTypes) => {
				this.assetTypes = assetTypes;
				this.selectAssetType = assetTypes[0] ? assetTypes[0].value : null;
			});
			this.loadAssetPropertyGroups();
			this.refresh();
		},
		onBackPress() {
			uni.hideLoading();
		},
		methods: {
			refresh() {
				this.queryPageAssets();
				this.closePop();
			},
			checkboxChange(val){
				this.batchConfirmation.instanceId=val.detail.value
			},
			scrollTop() {
				// if(this.pageNum > 1) {
				// 	--this.pageNum;
				// 	this.queryPageAssets();
				// }
			},
			scrollBottom() {
				if (this.pageNum < this.totalPage) {
					++this.pageNum;
					this.queryPageAssets(true);
				} else {
					uni.showToast({
						title: "没有更多数据了"
					})
				}
			},
			// onPageChange({type, current}) {
			// 	this.pageNum = current;
			// 	this.queryPageAssets();
			// },
			clear() {
				this.searchValue = null;
				this.search();
			},
			search() {
				this.pageNum = 1;
				this.$nextTick(this.queryPageAssets);
			},
			input() {},

			// 查询未确认资产列表
			queryPageAssets(appendFlag) {
				let params = {
					queryValue: this.searchValue,
					pageSize: this.pageSize,
					pageNum: this.pageNum,
					confirmStatus:"uncomfirm"
				}

				uni.showLoading({
					title: "加载中...",
					icon: "loading"
				})

				queryTableDataByCategory(params).then(res => {
					let {
						total,
						list
					} = res.data || {};
					this.totalAsset = total || 0;
					let records = list || [];
					if (appendFlag) {
						this.assetReocrds.push(...records);
					} else {
						this.assetReocrds = records;
					}
					console.log(" this.totalAsset ", this.totalAsset);
				}).catch(err => {}).then(() => {
					uni.hideLoading();
				});
			},
			showRegisterAssetTypes() {
				this.$refs.popup.open(this.popType);
			},
			closePop() {
				this.$refs.popup.close();
			},
			toEditAsset(assetRecord) {
				let assetModel = {
					type: assetRecord.categoryId,
					record: assetRecord
				}
				this.setCurrent(assetModel);
				// 跳转至资产编辑
				uni.navigateTo({
					url: "/pages/asset/edit_asset"
				})
			},
			showAssetDetail(assetRecord) {
				let assetModel = {
					type: assetRecord.categoryId,
					record: assetRecord
				}
				this.setCurrent(assetModel);
				// 跳转至详情
				uni.navigateTo({
					url: "/pages/asset/view_asset"
				})
			},
			confirmInstance(assetRecord,type) {
				let params = {
					"code": "confirmStatus",
					"value": "confirmd",
					"ids":type?assetRecord.instanceId:[assetRecord.instanceId],
				}

				uni.showLoading({
					title: "确认中...",
					icon: "loading"
				})
				confirmInstance(params).then(res => {
					uni.showToast({
						title: "确认成功",
						duration: 500
					});
					setTimeout(() => {
						this.refresh();
					}, 500);
				}).catch(err => {
					console.log(err);
					uni.showLoading({
						title: "确认失败！",
						icon: "loading"
					})
				}).then(() => {

				})

			}
		}
	}
</script>

<style lang="scss" scoped>
	.confirm-asset {
		font-size: 13pt;
		overflow: hidden;

		.asset-searchbar {
			:deep(.uni-searchbar__box) {
				justify-content: unset !important;
			}
		}

		.asset-content {
			/* #ifndef H5 */
			height: calc(100vh - 140px);
			/* #endif */
			/* #ifdef H5 */
			height: calc(100vh - 200px);

			/* #endif */
			&-bottom {
				/* #ifndef H5 */
				height: calc(100vh - 80px);
				/* #endif */
				/* #ifdef H5 */
				height: calc(100vh - 140px);
				/* #endif */
			}

			.empty-text {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				color: gray;
			}
		}

		// .unconfirm_asset_bg {
		// 	background-image: url('/static/asset/unconfirm_asset_bg.png'); 
		// }
		// .confirmed_asset_bg {
		// 	background-image: url('/static/asset/confirmed_asset_bg.png'); 
		// }

		.asset-item {
			margin: 5px 10px 10px;
			padding: 10px 20px;
			font-size: 13pt;

			.split-line {
				height: 1px;
				background-color: #E3E8F0;
				transform: scaleY(.7);
				margin: 8px 0;
			}

			:deep(.uni-section-header) {
				display: none;
			}

			.asset-title {
				display: flex;
				align-items: center;
				margin-bottom: 8pt;
				// .online-status {
				// 	background-color: gray;
				// 	width: 11.5pt; 
				// 	height: 11.5pt;
				// 	&-on {
				// 		background-color: #1CB91C;
				// 	}
				// }
			}

			.asset-column {
				line-height: 25px;

				.label {
					//margin-left: 10px;
				}
			}
		}

		.popup-content {
			max-height: 80vh;
			padding-bottom: 50px;
			position: relative;

			.asset-type {
				background-color: #F7F8FA;
				height: 30pt;
				font-size: .8em;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 5px;

				&-selected {
					background-color: #3190F9;
					color: white;
				}

				// text {
				// 	overflow: hidden;
				// 	white-space: nowrap;
				// 	text-overflow: ellipsis;
				// }
			}
		}


		.batch-confirm-btn {
			background-color: white;
			text-align: center;
			padding: 4px 2px;
			font-size: .9em;
			border-radius: 5px;
			width: 60px;
		}

		.confirm-btn {
			color: white;
			background-color: #007aff;
			text-align: center;
			padding: 1px;
			font-size: .9em;
			border-radius: 5px;
		}
	}
</style>