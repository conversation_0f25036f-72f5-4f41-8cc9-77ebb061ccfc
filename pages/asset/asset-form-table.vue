<template>
<view class="asset-form-table">
	
	<uni-forms-item class="text-item" :label="name">
		<view v-if="!textMode" style="color: white; font-size: .8em; background-color: #007aff; border-radius: 5px; padding: 5px 15px;" @click="toAddRecord">添加</view>
	</uni-forms-item>
	
	<uni-group class="row-group" v-for="(row,index) in tableRows" mode="card" :key="index" style="position: relative;">
		
		<uni-row class="property-row" v-for="(tableColumn,columnIndex) in tableList" :key="columnIndex" @click="toEditRecord(row)">
			<uni-col :span="8">
				<view class="label">{{tableColumn.name}}</view>
			</uni-col>
			<uni-col :span="16">
				<view class="value">{{getColumnValue(row, tableColumn)}}</view>
			</uni-col>
		</uni-row>
		
		<uni-icons v-if="!textMode" type="minus" style="font-size: 1.5em;position: absolute; right: 10px; top: 10px;" @click="removeRecord(index)"></uni-icons>
	</uni-group>
	
	
	<!-- 弹窗设计 -->
	<uni-popup ref="popup" background-color="#fff">
		<uni-section :title="name">
			<template #right>
				<uni-icons type="close" @click="closePop"></uni-icons>
			</template>
			
			<uni-forms label-width="120px">
				<scroll-view class="popup-content" :style="popupContentStyle" scroll-y>
					<uni-forms-item class="form-item" v-for="(tableColumn,columnIndex) in tableList" :key="columnIndex" :name="tableColumn.code" :label="tableColumn.name">
						<uni-data-select v-if="tableColumn.dataType == 'enum'" :placeholder="'请选择' + tableColumn.name"  v-model="record[tableColumn.code]" :localdata="getLocaldata(tableColumn)"></uni-data-select>
						<uni-easyinput v-else :placeholder="'请输入' + tableColumn.name" v-model="record[tableColumn.code]"></uni-easyinput>
					</uni-forms-item>
				</scroll-view>
			</uni-forms>
		</uni-section>
		<button type="primary" style="position: absolute;bottom: 0;width: 100%;" @click="saveRecord">确定</button>
	</uni-popup>
	
	
</view>
</template>

<script>
/**
 * 表单里面某个字段的表格类展现
 */
export default {
	props: {
		formModel: {
			type: Object
		},
		propertyModel: {
			type: Object,
			required: true
		},
		textMode: Boolean
	},
	data() {
		return {
			name: null,
			field: null,
			
			// 字段信息
			tableList: [],
			// 数据信息
			tableRows: [],
			
			// 新增或者编辑的信息
			record: {},
			popType: "add"
		}
	},
	created() {
		let {name, field, component} = this.propertyModel;
		this.name = name;
		this.field = field;
		this.tableList = component.tableList || [];
		this.updateTableRows();
	},
	methods: {
		// 回显
		updateTableRows() {
			if(!this.formModel || Object.keys(this.formModel).length == 0) return;
			
			let field = this.field;
			let arrayValue = this.formModel[field];
			
			this.tableRows = [];
			try {
				let rows = !arrayValue ? [] : JSON.parse(arrayValue);
				if(Array.isArray(rows)) {
					this.tableRows.push(...rows);
				}
			} catch(err) {
			}
		},
		updatePropertyValue() {
			let propertyValue = JSON.stringify(this.tableRows);
			let field = this.field;
			this.formModel[field] = propertyValue;
		},
		openPop() {
			this.$refs.popup.open("bottom");
		},
		closePop() {
			this.$refs.popup.close();
		},
		toAddRecord() {
			this.record = {};
			this.openPop();
			this.popType = "add";
		},
		toEditRecord(row) {
			this.record = row;
			this.openPop();
			this.popType = "edit";
		},
		saveRecord() {
			if(this.popType == "add") {
				this.tableRows.push(this.record);
			} else {
				// do nothing
			}
			this.updatePropertyValue();
			this.closePop();
		},
		removeRecord(index) {
			this.tableRows.splice(index, 1);
			this.updatePropertyValue();
		},
		
		getColumnValue(row, tableColumn) {
			let value = row[tableColumn.code];
			
			if(tableColumn.dataType == "enum") {
				let enumArray = tableColumn.enumArray || []; 
				let enumOption = enumArray.find(enumOption => enumOption.value == value);
				return enumOption && enumOption.label;
			}
			return value;
		},
		
		getLocaldata(tableColumn) {
			let enumArray = tableColumn.enumArray || []; 
			return enumArray.map(option => {
			   let {label, value} = option;
			   return {
				   text: label,
				   value
			   }
		   });
		}
	},
	computed: {
		popupContentStyle() {
			let style = {};
			let height = Math.min(this.tableList.length * 38 + 240, 500) ;
			style.height = height + "px";
			return style;
		}
	},
	watch: {
		formModel() {
			this.updateTableRows();
		}
	}
}
</script>

<style lang="scss" scoped>
.asset-form-table {
	.text-item {
		:deep(.uni-forms-item__content) {
			display: flex;
			justify-content: center;
		}
	}
	.row-group {
		padding: 20px 0 10px;
		color: #606266;
		font-size: .9.5em;
		.property-row {
			line-height: 25px;
			margin: 5px;
			border-bottom: 1px #eee solid;
			padding-bottom: 10px;
			.label {
				margin-left: 5px;
			}
		}
	}
	
	.popup-content {
		position: relative;
		padding: 20px;
		.form-item {
			padding-right: 25px;
		}
	}
}
</style>