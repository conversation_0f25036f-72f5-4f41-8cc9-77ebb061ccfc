<template>
	<view class="register-asset">
		<view class="main-form" >
			<!-- 资产form -->
			<asset-form ref="assetForm" :asset-type="assetType" :model="instance">
				<template #bottom>
					<!-- 留点空白区域防止下拉选项在最底下无法显示 -->
					<uni-section style="height: 60px;" title=""></uni-section>
				</template>
			</asset-form>
		</view>
		<uni-row :gutter="20" style="position: absolute;bottom: 0;width: 100%;">
			<uni-col :span="12">
				<button type="default" @click="back">取消</button>
			</uni-col>
			<uni-col :span="12">
				<button type="primary" @click="saveAsset">保存</button>
			</uni-col>
		</uni-row>
	</view>
</template>

<script>
import assetForm from "./asset-form.vue"
import asset from "./asset.js"
import {saveAppInstance, queryAppInstanceById} from "./api/index.js"
export default {
	components: {
		assetForm
	},
	mixins: [asset],
	onLoad(data) {
		let {type, record} = this.getCurrent();
		this.assetType = type;
		// 设置导航标题
		uni.setNavigationBarTitle({
			title: record.categoryName + ' - ' + record.name
		});
		
		let instanceId = record.instanceId;
		uni.showLoading({
			title: "正在查询实例信息",
			icon: "loading"
		})
		queryAppInstanceById(instanceId).then(res => {
			this.instance = res.data || {};
		}).catch(err => {}).then(() => {
				uni.hideLoading();
			});
	},
	onBackPress() {
		uni.hideLoading();
	},
	data() {
		return {
			assetType: null,
			instance: {}
		}
	},
	methods:{
		back(){
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			 let pages = getCurrentPages(); // 当前页面
			 let beforePage = pages[pages.length - 2]; // 前一个页面
			 uni.navigateBack({
			     success: function() {
			         typeof(beforePage.refresh) == 'function' && beforePage.refresh(); 
			     }
			 });
		},
		saveAsset() {
			
			this.$refs.assetForm.validate().then(() => {
				let instance = this.instance;
				uni.showLoading({
					title: "正在保存...",
					icon: "loading"
				})
				saveAppInstance(instance).then(res => {
					uni.showToast({
						title: "保存成功",
						duration: 500
					});
					setTimeout(() => {
						this.backAndRefrush();
					}, 500);
				}).catch(err => {
					console.error(err);
				}).then(() => {
					uni.hideLoading();
				});
			}).catch(data => {
				console.log(data);
				let msg = "校验未通过";
				if(Array.isArray(data)) {
					let firstField = data[0].key;
					this.$refs.assetForm.scrollPropertyToView(firstField);
				}
				uni.showToast({
					title: msg,
					duration: 1000
				})
			});
			// 暂时返回
			// this.backAndRefrush();
		}
	}
}
</script>

<style lang="scss" scoped>
.register-asset {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px); 
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px); 
		/* #endif */
		overflow: auto;
	}
}
</style>