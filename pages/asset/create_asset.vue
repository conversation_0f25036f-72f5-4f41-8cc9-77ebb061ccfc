<template>
	<view class="create-asset">
		<view>{{model}}</view>
		<!-- 创建资源 -->
		<uni-forms label-width="130px" border>
			<uni-group title="基本信息">
				<form-data-item v-for="(item,index) in items" :form-model="model" :data="item" :key="index"></form-data-item>
			</uni-group>
			
			<uni-group title="管理信息">
				<form-data-item v-for="(item,index) in items" :form-model="model" :data="item" :key="index"></form-data-item>
			</uni-group>
			
		</uni-forms>
	</view>
</template>

<script>
export default {
	data() {
		return {
			model: {
			},
			items: [
				{
				   "name":  "资产名称",
				   "field": "asset_name",
				   "component": {
					   "type": "Input",
					   "props": {
						  "placeholder": "请输入资产名称"
					   }
					}
				},
				{
				   "name":  "资产类别",
				   "field": "asset_type",
				   "component": {
					   "type": "Select",
					   "props": {
						  "placeholder": "请选择资产类别"
					   },
					   "options": [
						   {"label": "服务器", "value": "server"},
						   {"label": "交换机", "value": "switch"},
					   ]
					}
				},
				{
				   "name":  "数字类型",
				   "field": "num",
				   "component": {
					   "type": "Number",
					   "props": {
						  "placeholder": "请输入年限"
					   }
					}
				},
				{
				   "name":  "日期类型",
				   "field": "datexxx",
				   "component": {
					   "type": "DatePicker",
					   "props": {
						  "type": "daterange",
						  "placeholder": "请选择日期类型"
					   }
					}
				}
			]
		}
	}
}
</script>

<style lang="scss" scoped>
.create-asset {
	
}
</style>