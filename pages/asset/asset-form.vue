<template>
<uni-forms ref="form" class="asset-form"  :modelValue="model" :rules="assetPropertyRules(assetPropertyGroups)" :label-width="labelWidth" :border="border">
	<uni-section v-for="(assetPropertyGroup, index) in assetPropertyGroups" :key="index" :title="assetPropertyGroup.groupName" type="line">
		<uni-group>
			<template v-for="(propertyModel,index) in filterChildren(assetPropertyGroup.children)">
				<asset-form-table v-if="isTableProperty(propertyModel)" :property-model="propertyModel" :text-mode="textMode" :form-model="model" :key="index + '-table'"></asset-form-table>
				<form-data-item v-else :class="classList(propertyModel)" :text-mode="textMode" enum-label-suffix="__label" :form-model="model" :property-model="propertyModel" :enum-request="queryAssetEnum" :key="index"></form-data-item>
			</template>
		</uni-group>
	</uni-section>
	
	<slot name="bottom"></slot>
</uni-forms>
</template>

<script>
import asset from "./asset.js"
import assetFormTable from "./asset-form-table.vue"
import {queryEnumListByProCate} from "./api/index.js"
export default {
	name: "asset-form",
	mixins: [asset],
	components: {assetFormTable},
	props: {
		labelWidth: {
			type: String,
			default: "130px"
		},
		border:{
			type: Boolean,
			default: true
		},
		assetType: {
			type: [String, Number],
			required: true
		},
		// 表单模型
		model: Object,
		/** 只读模式/查看详情 */
		textMode: Boolean
	},
	data() {
		return {
			assetPropertyGroups: []
		}
	},
	created() {
		uni.showLoading({
			title: "正在加载资产属性...",
			icon: "loading"
		})
		this.getAssetPropertyGroup(this.assetType, (result) => {
			this.assetPropertyGroups = result;
			uni.hideLoading();
		});
	},
	methods: {
		queryAssetEnum(propertyModel, queryValue) {
			let {field: code} = propertyModel;
			// {"code":"zoneName","categoryId":"125","queryValue":"东莞市"}
			let params = {
				code,
				categoryId: this.assetType,
				queryValue,
				limit: 50
			}
			return queryEnumListByProCate(params);
		},
		filterChildren(children) {
			return (children || []).filter(child => {
				let {component} = child || {};
				let {attrs} = component || {};
				attrs = attrs || {};
				let hiddenFlag = attrs.hidden;
				if(!hiddenFlag) return true;
				return hiddenFlag == '0' || hiddenFlag == 'false';
			}).map(child => {
				let {component} = child || {};
				let {attrs} = component || {};
				attrs = attrs || {};
				
				let readonlyFlag = !attrs.readonly ? "0" : attrs.readonly + "";
				let requiredFlag = !attrs.required ? "0" : attrs.required + "";
				
				let readonly = readonlyFlag == "true" || readonlyFlag == "1";
				let required = requiredFlag == "true" || requiredFlag == "1";
				return {
					...child,
					readonly,
					required
				};
			});
		},
		
		/**
		 * 遍历构建表单的校验模型
		 * 
		 * @param {Object} groups
		 */
		assetPropertyRules(groups) {
			let assetPropertyRules = {};
			for(let group of groups || []) {
				let children = this.filterChildren(group.children);
				for(let childModel of children) {
					let {required, name, field} = childModel;
					if(required) {
						assetPropertyRules[field] = {
							rules: [
								{
									required: true,
									errorMessage: `${name}不能为空`
								}
							]
						}
					}
				}
			}
			return assetPropertyRules;
		},
		
		/**
		 * 调用表单的校验方法
		 */
		validate() {
			return this.$refs.form.validate();
		},
		
		/**
		 * form-item 添加样式方便query selector 定位dom
		 * 
		 * @param {Object} propertyModel
		 */
		classList(propertyModel) {
			let classList = [];
			if(propertyModel.field) {
				classList.push('form-item-field-' + propertyModel.field);
			}
			return classList;
		},
		
		/**
		 * 将指定属性滚动到可视区域
		 * @param {Object} field
		 */
		scrollPropertyToView(field) {
			try {
				let propertyDom = this.$el.querySelector(`.form-item-field-` + field);
				if(propertyDom) {
					propertyDom.scrollIntoView();
				}
			} catch(err) {
				console.error(err);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.asset-form {
}
</style>