<template>
	<view class="query-asset" style="background-color: #fff;">
		<view style="background-color: #fff;padding: 26rpx;">
			<view
				style="background-color: #fff;display: grid;grid-template-columns: repeat(3, 1fr);text-align: center;margin-top: 16rpx;cursor: pointer;">
				<view style="border: 2rpx solid #ddd;">日</view>
				<view style="border: 2rpx solid #ddd;">周</view>
				<view style="border: 2rpx solid #ddd;">月</view>
			</view>
			<view style="margin-top: 16rpx;">
				<view style="padding: 26rpx 0;">告警数据统计</view>
				<view style="display: flex;justify-content: space-around;">
					<image src="/static/images_new/alarm.png" mode="scaleToFill"
						style="width: 100rpx;height: 100rpx;border-radius: 16rpx;" />
					<view>
						<view style="font-size: 30rpx;color: #666;">总告警数</view>
						<view style="font-size: 43rpx;font-weight: 500;">336</view>
					</view>
					<view>
						<view style="font-size: 30rpx;color: #666;">紧急告警数</view>
						<view style="color: #f40;font-size: 43rpx;font-weight: 500;">336</view>
					</view>
					<view>
						<view style="font-size: 30rpx;color: #666;">重要告警数</view>
						<view style="color: orange;font-size: 43rpx;font-weight: 500;">336</view>
					</view>
				</view>
			</view>
			<view style="margin-top: 66rpx;">
				<view style="padding: 26rpx 0;">处置占比统计</view>
				<view style="display: flex;justify-content: space-around;">
					<view
						style="height: 166rpx;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
						<view style="font-size: 30rpx;color: #666;">总派单数</view>
						<view style="font-size: 69rpx;font-weight: 500;">336</view>
					</view>
					<view style="height: 166rpx; width: 166rpx;">
						<view style="font-size: 30rpx;color: #666;text-align: center;">处置率</view>
						<view style="height: 166rpx; flex-shrink: 0;flex-grow: 1;">
							<l-echart style="flex-shrink: 0;" ref="chartRef01"></l-echart>
						</view>
					</view>
				</view>
				<view style="margin-top: 26rpx;">
					<view style="height: 566rpx">
						<l-echart ref="chartRef02"></l-echart>
					</view>
				</view>
			</view>

			<view style="margin-top: -49rpx;">
				<view style="padding: 26rpx 0;display: flex;justify-content: space-around; align-items: center;">
					告警数量趋势（小时/天）
					<uni-data-select v-model="value" :localdata="range" @change="change"></uni-data-select>
				</view>
				<view style="height: 650rpx">
					<l-echart ref="chartLine01"></l-echart>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import asset from "./asset.js"
import { queryTableDataByCategory } from "./api/index.js"
import * as echarts from 'echarts'
export default {
	mixins: [asset],
	data() {
		return {
			value: 0,
			range: [
				{ value: 0, text: "告警总数" },
				{ value: 1, text: "严重告警数" },
				{ value: 2, text: "重要告警数" },
			],
			option: {
				series: [{
					type: 'gauge',
					startAngle: 90,
					endAngle: -270,
					pointer: {
						show: false
					},
					grid: {
						left: 20,
						right: 20,
						bottom: 15,
						top: 10,
						containLabel: true
					},
					progress: {
						show: true,
						overlap: false,
						roundCap: true,
						clip: false,
						itemStyle: {
							// borderWidth: 1,
							// borderColor: '#464646'
						}
					},
					axisLine: {

						lineStyle: {
							width: 6
						}
					},
					splitLine: {
						show: false,
						distance: 0,
						length: 10
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: false,
						distance: 50
					},
					data: [
						{
							value: 66,
							detail: {
								offsetCenter: ['0%', '0%']
							}
						}
					],
					detail: {
						// width: 50,
						// height: 14,
						fontSize: 13,
						//color: 'auto',
						// borderColor: 'auto',
						// borderRadius: 20,
						// borderWidth: 1,
						formatter: '{value}%'
					}
				}]
			},
			optionBrokenLine: {
				xAxis: {
					type: 'category',
					data: ['03-11', '03-13', '03-16', '03-19', '03-20', '03-22', '03-26']
				},
				yAxis: {
					type: 'value'
				},
				series: [{
					data: [150, 250, 190, 266, 333, 290, 366],
					type: 'line',
					smooth: true,
					areaStyle: {}

				}]
			},
		}
	},
	onLoad(page) {

	},
	computed: {

	},
	mounted() {
		this.init();
	},
	onBackPress() {

	},
	methods: {
		async init() {
			this.$refs.chartRef01.init(echarts, chart => {
				chart.setOption(this.option);
			});
			this.$refs.chartRef02.init(echarts, chart => {
				let option = {
					tooltip: {
						trigger: 'item'
					},
					legend: {
						orient: 'vertical',      // 改为垂直方向排列
						left: '26rpx',        // 图例靠左
						top: 'center',           // 垂直居中
						align: 'left',           // 文字左对齐（避免图标与文字间距过大）
						itemGap: 20              // 增大图例项间距
					},
					series: [
						{
							name: '访问来源',
							type: 'pie',
							center: ['66%', '50%'],  // 圆心向右偏移（留出左侧给图例）
							radius: ['40%', '70%'],  // 适当缩小半径
							avoidLabelOverlap: false,
							label: {
								show: false,
								position: 'center'
							},
							emphasis: {
								label: {
									show: true,
									fontSize: '20',
									fontWeight: 'bold'
								}
							},
							labelLine: {
								show: false
							},
							data: [
								{ value: 1048, name: '处理中' },
								{ value: 735, name: '已处理' },
								{ value: 580, name: '已超时' },
							]
						}
					]
				}
				chart.setOption(option);

			});
			this.$refs.chartLine01.init(echarts, chart => {
				chart.setOption(this.optionBrokenLine);

			});
		},
	}
}
</script>

<style lang="scss" scoped></style>