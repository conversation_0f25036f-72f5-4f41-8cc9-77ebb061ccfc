import {queryAppAllCategory, queryCateProByApp} from "./api/index.js"
let allAssetPropertyGroupMap = {};
// 资产列表
let assetCategoryList = [];
// 资产上下文
let assetContext = {
	current: {
		type: null,
		record: {}
	}
}

export default {
	data() {
		return {
		}
	},
	
	methods: {
		/** 资产类型列表 */
		queryAssetCategorys(callback) {
			if(assetCategoryList.length == 0) {
				queryAppAllCategory().then(res => {
					assetCategoryList.push(...res.data);
					callback && callback(assetCategoryList);
				}).catch(err => {
					callback && callback([]);
				}); 
			} else {
				callback && callback(assetCategoryList);
			}
		},
		
		/* 查询所有类别的属性（一般只需要请求一次）*/
		loadAssetPropertyGroups(callback) {
			if(Object.keys(allAssetPropertyGroupMap).length == 0) {
				queryCateProByApp().then(res => {
					allAssetPropertyGroupMap = res.data || {};
				}).catch(err => {}).then(() => {
					callback && callback();
				});
			}
		},
		
		// 获取某一个资产类型的属性分组
		getAssetPropertyGroup(assetType, callback) {
			
			// 已经加载完成
			if(Object.keys(allAssetPropertyGroupMap).length > 0) {
				let result = allAssetPropertyGroupMap[assetType];
				callback && callback(result);
				return result;
			}
			
			// 开始加载
			this.loadAssetPropertyGroups(() => {
				let result = allAssetPropertyGroupMap[assetType];
				callback && callback(result);
				return result;
			});
			
			return null;
		},
		
		// 是否表格类的属性字段
		isTableProperty(propertyModel) {
			let {component } = propertyModel || {};
			return component && component.type == "Table";
		},
		
		setCurrent(assetModel) {
			assetContext.current = assetModel;
		},
		getCurrent() {
			return assetContext.current;
		}
	}
}