<template>
	<view class="view-asset">
		<asset-form :asset-type="assetType" :model="assetRecord" text-mode></asset-form>
	</view>
</template>

<script>
import assetForm from "./asset-form.vue"
import asset from "./asset.js"
import {queryAppInstanceById} from "./api/index.js"
export default {
	props: {
	},
	mixins: [asset],
	components: {assetForm},
	data() {
		return {
			assetType: null,
			assetRecord: {}
		}
	},
	onLoad(data) {
		let {type, record} = this.getCurrent();
		this.assetType = type;
		// 设置导航标题
		uni.setNavigationBarTitle({
			title: record.categoryName + ' - ' + record.name
		});
		
		let instanceId = record.instanceId;
		let loading = uni.showLoading({
			title: "正在查询实例信息",
			icon: "loading"
		})
		queryAppInstanceById(instanceId).then(res => {
			this.assetRecord = res.data || {};
		}).catch(err => {}).then(() => {
				uni.hideLoading();
			});
	},
}
</script>

<style lang="scss" scoped>
.view-asset {
	
}
</style>