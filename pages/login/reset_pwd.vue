<template>
	<view class="container">
		<view class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#4d2574" :size="20"></tui-icon>
						<input :value="password" placeholder="请输入原密码" :password="true" placeholder-class="tui-phcolor"
							type="text" maxlength="40" @input="inputOrigPwd" />
						<view class="tui-icon-close" v-show="password" @tap="clearInput(1)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#4d2574" :size="20"></tui-icon>
						<input :value="newpassword1" placeholder="请输入八位及以上的新密码" :password="true"
							placeholder-class="tui-phcolor" type="text" maxlength="40" @input="inputPwd" />
						<view class="tui-icon-close" v-show="newpassword1" @tap="clearInput(2)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#4d2574" :size="20"></tui-icon>
						<input :value="newpassword2" placeholder="请再次确认密码" :password="true"
							placeholder-class="tui-phcolor" type="text" maxlength="40" @input="inputPwd2" />
						<view class="tui-icon-close" v-show="newpassword2" @tap="clearInput(3)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-btn-box">
				<tui-button :disabledGray="true" :disabled="disabled" :shadow="true" shape="circle" @tap="retPwd">保存
				</tui-button>
			</view>
		</view>
	</view>
</template>

<script>
import { sm3 } from 'sm-crypto'
import crypto from 'crypto'
import form from "@/common/validation.js"
import util from "@/common/util.js"
import securityStorage from '@/common/securityStorage'
import tuiButton from "@/components/tui/tui-button"
import tuiIcon from "@/components/tui/tui-icon"
import tuiListCell from "@/components/tui/tui-list-cell"
let time = Date.parse(new Date()) / 1000
// import md5 from 'blueimp-md5';
export default {
	computed: {
		disabled: function () {
			let bool = true;
			if (this.password && this.newpassword1 && this.newpassword2) {
				bool = false;
			}
			return bool;
		}
	},
	components: {
		tuiButton,
		tuiIcon,
		tuiListCell,
	},
	data() {
		return {
			user: [],
			origPassword: '',
			password: '',
			newpassword1: '',
			newpassword2: '',
			isSend: false,
			codeTime: 0,
			color: "#ffffff",
			background: "#a778d4",
			post_data: {}
		};
	},
	onShow() {
		this.$H.checkLoginAndJumpStart();//检查登录状态
	},
	onLoad() {
		let token = uni.getStorageSync('token');
		this.user = securityStorage.getStorageSync('user');// uni.getStorageSync('user');
	},
	methods: {
		inputOrigPwd: function (e) {
			this.password = e.detail.value;
		},
		inputPwd: function (e) {
			this.newpassword1 = e.detail.value;
		},
		inputPwd2: function (e) {
			this.newpassword2 = e.detail.value;
		},
		clearInput(type) {
			switch (type) {
				case 1:
					this.password = '';
					break;
				case 2:
					this.newpassword1 = '';
					break;
				case 3:
					this.newpassword2 = '';
					break;
				default:
					break;
			}
		},
		// 新增 MD5 工具函数
		async md5Browser(str) {
			const encoder = new TextEncoder();
			const data = encoder.encode(str);
			const hashBuffer = await crypto.subtle.digest('MD5', data);
			const hashArray = Array.from(new Uint8Array(hashBuffer));
			return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
		},
		// retPwd() {
		// 	let that = this;
		// 	let post_data = {
		// 		password: that.password,
		// 		newpassword1: that.newpassword1,
		// 		newpassword2: that.newpassword2
		// 	};

		// 	// ▼▼▼▼▼▼▼▼▼▼ 表单验证规则（完整保留） ▼▼▼▼▼▼▼▼▼▼
		// 	let rules = [{
		// 		name: "password",
		// 		rule: ["required", "isEnAndStr"],
		// 		msg: ["请输入密码", "原始密码为8~20位数字、字母或特殊字符组合"]
		// 	}, {
		// 		name: "newpassword1",
		// 		rule: ["required", "isEnAndStr"],
		// 		msg: ["请输入密码", "新密码为8~20位数字、字母或特殊字符组合"]
		// 	}, {
		// 		name: "newpassword2",
		// 		rule: ["required", "isSame:newpassword1"],
		// 		msg: ["请输入确认密码", "两次输入的密码不一致"]
		// 	}];
		// 	// ▲▲▲▲▲▲▲▲▲▲ 表单验证规则 ▲▲▲▲▲▲▲▲▲▲

		// 	let checkRes = form.validation(post_data, rules);
		// 	if (!checkRes) {
		// 		if (that.password == that.newpassword1) {
		// 			uni.showToast({ title: "新旧密码不能相同", icon: "none" });
		// 			return;
		// 		}

		// 		// ▼▼▼▼▼▼▼▼▼▼ MD5计算改造部分（完整代码） ▼▼▼▼▼▼▼▼▼▼
		// 		// 生成用户账号的MD5（完全替代crypto.createHash）
		// 		const accountMd5 = md5(that.user.用户账号);

		// 		// 构造请求数据
		// 		post_data.newPwd = sm3(that.newpassword1);
		// 		post_data.oldPwd = sm3(that.password);
		// 		post_data.oldPwd2 = accountMd5;  // 直接使用计算结果
		// 		post_data.token = uni.getStorageSync('token');
		// 		// ▲▲▲▲▲▲▲▲▲▲ MD5计算改造部分 ▲▲▲▲▲▲▲▲▲▲

		// 		// ▼▼▼▼▼▼▼▼▼▼ 敏感字段过滤（完整保留） ▼▼▼▼▼▼▼▼▼▼
		// 		let { password, newpassword1, newpassword2, ...otherProps } = post_data;

		// 		that.$H.post('/framework/sysmanage/users/modifypwd', otherProps).then(res => {
		// 			if (res.status == 0) {
		// 				uni.showToast({ title: "密码修改成功", icon: "none" });
		// 				setTimeout(() => { uni.navigateBack(); }, 2500);
		// 			} else {
		// 				setTimeout(() => {
		// 					uni.showToast({ title: res.msg, icon: "none" })
		// 				}, 1000);
		// 			}
		// 		}).catch(err => {
		// 			console.error("API请求失败:", err);
		// 			uni.showToast({ title: "网络异常，请重试", icon: "none" });
		// 		});
		// 	} else {
		// 		uni.showToast({ title: checkRes, icon: "none" });
		// 	}
		// }
		retPwd() {
			let that = this
			let post_data = {
				password: that.password,
				newpassword1: that.newpassword1,
				newpassword2: that.newpassword2
			}
			//表单规则
			let rules = [{
				name: "password",
				rule: ["required", "isEnAndStr"],
				msg: ["请输入密码", "原始密码为8~20位数字、字母或特殊字符组合"]
			}, {
				name: "newpassword1",
				rule: ["required", "isEnAndStr"],
				msg: ["请输入密码", "新密码为8~20位数字、字母或特殊字符组合"]
			}, {
				name: "newpassword2",
				rule: ["required", "isSame:newpassword1"],
				msg: ["请输入确认密码", "两次输入的密码不一致"]
			},];
			//进行表单检查
			let checkRes = form.validation(post_data, rules);
			if (!checkRes) {
				console.log('password', that.password, 'newpassword1', that.newpassword1);
				if (that.password == that.newpassword1) {
					uni.showToast({
						title: "新旧密码不能相同",
						icon: "none"
					})
					return;
				}
				post_data.newPwd = sm3(that.newpassword1);
				post_data.oldPwd = sm3(that.password);
				var md5 = crypto.createHash("md5");
				md5.update(that.user.用户账号);
				var p = md5.digest("hex");
				post_data.oldPwd2 = p; //对应接口的用户ID
				post_data.token = uni.getStorageSync('token');
				let { password, newpassword1, newpassword2, ...otherProps } = post_data;
				that.$H.post('/framework/sysmanage/users/modifypwd', otherProps).then(res => {
					if (res.status == 0) {
						uni.showToast({
							title: "密码修改成功",
							icon: "none"
						})
						setTimeout(() => {
							uni.navigateBack();
						}, 2500);
					} else {
						setTimeout(() => {
							uni.showToast({
								title: res.msg,
								icon: "none"
							})
						}, 1000);
					}
				})
			} else {
				uni.showToast({
					title: checkRes,
					icon: "none"
				});
			}
		},
	}
};
</script>

<style lang="scss" scoped>
.container {
	.tui-page-title {
		width: 100%;
		font-size: 48rpx;
		font-weight: bold;
		//color: $uni-text-color;
		line-height: 42rpx;
		padding: 110rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
		text-align: center;
	}

	.tui-form {
		padding-top: 50rpx;

		.tui-view-input {
			width: 100%;
			box-sizing: border-box;
			padding: 0 40rpx;

			.tui-cell-input {
				width: 100%;
				display: flex;
				align-items: center;
				padding-top: 10rpx;
				//padding-bottom: $uni-spacing-col-base;

				input {
					flex: 1;
					//padding-left: $uni-spacing-row-base;
				}

				.tui-icon-close {
					margin-left: auto;
				}

				.tui-btn-send {
					width: 156rpx;
					height: 50rpx;
					text-align: center;
					padding-top: 10rpx;
					flex-shrink: 0;
					//font-size: $uni-font-size-base;
					color: #ffffff;
					//background: $uni-text-color; //
				}

				.tui-gray {
					//color: $uni-text-color-placeholder;
					background: #1cc6e0;
					padding-top: 20rpx;
				}
			}
		}

		.tui-cell-text {
			width: 100%;
			//padding: 40rpx $uni-spacing-row-lg;
			box-sizing: border-box;
			//font-size: $uni-font-size-sm;
			//color: $uni-text-color-grey;
			display: flex;
			align-items: center;

			.tui-color-primary {
				color: #55aaff;
				//padding-left: $uni-spacing-row-sm;
			}
		}

		.tui-btn-box {
			width: 100%;
			//padding: 0 $uni-spacing-row-lg;
			box-sizing: border-box;
			margin-top: 80rpx;
		}
	}
}
</style>
