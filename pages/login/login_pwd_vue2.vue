<template>
	<view class="content">
		<view style="height:60rpx;"></view>
		<view style="display: flex;justify-content: center; align-items: center; flex-direction: column;">
			<text class="u-demo-block__title" style="margin-top: 25rpx;font-size: 60rpx; color: #fff;">移动运维管理平台</text>
			<text class="u-demo-block__title"
				style="margin-top: 25rpx;font-size: 32rpx;color: #fff;">随时随地访问，高效便捷运维</text>
		</view>

		<image style="
		    width: 376px;
			height: 388rpx;
			margin: 0 auto;
			display: flex;
			margin-top: 100rpx;
		" src="/static/images_new/login/u2417.svg" mode="scaleToFill" />
		<!-- <view style="display: flex;justify-content: center; align-items: center; flex-direction: column;">
			<text class="u-demo-block__title" style="margin-top: 25rpx;font-size: 20pt;">账号密码登录</text>
			<text class="u-demo-block__title"
				style="margin-top: 25rpx;font-size: 10pt;color: #5A7096;">请使用已注册的账号密码</text>
		</view> -->
		<uni-forms ref="loginForm" :rules="customRules" :modelValue="loginForm" style="padding: 40px;">
			<uni-forms-item style="margin-bottom: 0;" name="account">
				<uni-easyinput class="uni-easyinput-input" prefix-icon="person" v-model="loginForm.account" placeholder="请输入用户帐号" />
			</uni-forms-item>
			<uni-forms-item name="checkPass">
				<uni-easyinput class="uni-easyinput-input" prefix-icon="locked" v-model="loginForm.checkPass" type="password"
					placeholder="请输入登录密码" />
			</uni-forms-item>
			<!-- <uni-forms-item v-if="isCode" name="verifyCode">
				<uni-easyinput prefix-icon="locked" v-model="loginForm.verifyCode" placeholder="请输入验证码">
					<template #right>
						<image :src="'data:image/png;base64,' + codeIco" class="image-code" @click="getCode"></image>
					</template>
</uni-easyinput>
</uni-forms-item> -->
			<!-- <uni-forms-item>
				<checkbox-group @change="setAgree">
					<checkbox :checked="isAgree" style="transform: scale(0.7);margin-left: 40px;" />
					<text class="tui-color-primary" @click="toUserAgreement">用户服务协议</text>
					<text class="tui-color-primary">、</text>
					<text class="tui-color-primary" @click="toPrivacyAgreement">隐私政策</text>
				</checkbox-group>
			</uni-forms-item> -->
			<uni-forms-item>
				<button type="primary" style="margin-top: 40px;" shape="circle" :disabled="disabled" :loading="logining"
					@click="verificationUser">
					登录
				</button>
			</uni-forms-item>
			<uni-forms-item>
				<view style="text-align: center;">
					<text style="margin-top: 20px;color: #fff; font-weight: 550;" @click="toSmsLogin">
						使用手机验证码登录
					</text>
				</view>
			</uni-forms-item>


		</uni-forms>

		<!-- <view class="tui-cell-text">
			<view class="tui-color-primary" hover-class="tui-opcity" :hover-stay-time="150" @tap="href(1)">忘记密码？</view>
			<view hover-class="tui-opcity" :hover-stay-time="150">
				没有账号？
				<text class="tui-color-primary" @tap="href(2)">注册</text>
			</view>
		</view> -->
		<!-- <view style="display: flex;justify-content: center;align-items: center;margin-top: 20px;">
			<u-divider style="width: 50%;" text="短信登录方式"></u-divider>
		</view>
		<view class="tui-auth-login" style="display: flex;justify-content: center;align-items: center;">
			<view class="tui-icon-platform" hover-class="tui-opcity" :hover-stay-time="150" @tap="toSmsLogin">
				<image src="/static/images/sms.png" class="tui-login-logo"></image>
			</view>
		</view> -->
	</view>
</template>

<script>
// import crypto from 'crypto'
import md5 from 'js-md5'
import {
	sm3
} from 'sm-crypto'
import $C from '@/common/config.js'
import securityStorage from '@/common/securityStorage'
import tuiIcon from "@/components/tui/tui-icon"
import tuiBottomPopup from "@/components/tui/tui-bottom-popup"
export default {
	mounted() {
	},
	onReady() {
		// 获取验证码
		this.getCode();
	},
	watch: {
		loginForm(e) {
		}
	},
	computed: {
		disabled: function () {
			let bool = true;
			let verify = false;
			if (this.isCode) {
				verify = this.loginForm.verifyCode;
			} else {
				verify = true;
			}
			if (this.loginForm.account && this.loginForm.checkPass && verify && this.isAgree) {
				bool = false;
			}
			return bool;
		}
	},
	components: {
		tuiBottomPopup,
		tuiIcon,
	},
	data() {
		return {
			isAgree: true,
			isCode: false,
			codeIco: "",
			src: 'https://cdn.uviewui.com/uview/album/6.jpg',
			logining: false,
			loginForm: {
				account: "",//wenjianpeng dev
				checkPass: "",//1qaz!QAZ 3edc$RFV 
				verifyCode: "",
				rnd: "",
				isRemember: false,
			},
			// 自定义表单校验规则
			customRules: {
				account: {
					rules: [{
						required: true,
						errorMessage: '用户名不能为空'
					}]
				},
				checkPass: {
					rules: [{
						required: true,
						errorMessage: '密码不能为空'
					}]
				}
			}
		}
	},
	onShow() {
		//this.checkAndCleanData();//清除缓存，解决更新包后和网络切换后，验证码不出现的问题
		// this.getCode();

		// uni.getNetworkType({
		// 	success:function(res){
		// 		if (res.networkType == 'none' || res.networkType == 'unknown') {
		// 			uni.showModal({
		// 				title: '网络链接失败',
		// 				content: '检测到网络权限可能设置为关闭，您可以在“设置”中检查网络。或设置正确的VPN。'
		// 			})
		// 		}else{
		// 			console.log("【网络状态检测】：可用");
		// 		}
		// 	}
		// })
	},
	methods: {
		toUserAgreement() {
			uni.navigateTo({
				url: "./agreement"
			})
		},
		toPrivacyAgreement() {
			uni.navigateTo({
				url: "./agreement_privacy"
			})
		},
		setAgree(e) {
			this.isAgree = !this.isAgree
			this.$emit('setAgree', this.isAgree)
		},
		clearInput(type) {
			if (type == 1) {
				this.loginForm.account = '';
			} else {
				this.loginForm.checkPass = '';
			}
		},
		// 获取验证码
		getCode() {
			let that = this
			that.$H.get($C.portalContextPath + '/login/verifycode', {
				id: 5
			}).then(res => {
				// console.log("getCode", JSON.stringify(res));
				if (res.status == "0") {
					if (res.data.requireVerifyCode == 'false') {
						this.isCode = false;
					} else {
						this.isCode = true;
						this.codeIco = res.data.image;
						this.loginForm.rnd = res.data.rnd;
					}
				}
			})
		},
		// 获取用户信息
		getUserInfo(userId) {
			this.$H.get($C.portalContextPath + '/framework/sysmanage/eam/user/getCurrentUserDetailInfo').then(res => {
				if (res.status == "0") {
					securityStorage.setStorageSync('user', res.data);
				}
			});
		},
		verificationUser() {

			// if(true) {
			// 	this.$H.href("/pages/index", 2);
			// 	return;
			// }

			let self = this;
			let isLoginSuccess = false;
			if (this.loginForm.account.length < 2) {
				this.showMessage("帐号长度不少于2");
				return;
			}
			if (this.loginForm.checkPass.length < 4) {
				this.showMessage("口令长度不少于4");
				return;
			}
			self.logining = true;
			// var md5 = crypto.createHash("md5");
			// md5.update(this.loginForm.checkPass);
			var p = md5(this.loginForm.checkPass);
			let pwd = sm3(this.loginForm.checkPass);
			console.log("$C.getBaseUrl() + $C.portalContextPath=======", $C.getBaseUrl());
			// 生成随机 boundary
			const boundary = '----WebKitFormBoundary' + Math.random().toString(16).substr(2);

			// 手动拼接 FormData 格式
			let data = `--${boundary}\r\n`;
			data += 'Content-Disposition: form-data; name="u"\r\n\r\n';
			data += self.loginForm.account + '\r\n';
			data += `--${boundary}\r\n`;
			data += 'Content-Disposition: form-data; name="p"\r\n\r\n';
			data += pwd + '\r\n';
			data += `--${boundary}--`;
			let url = '';
			// #ifndef APP-PLUS
			url = '/new'
			// #endif
			console.log('手机-iphone', $C.getBaseUrl() + url + '/login');
			let tmpurl = '/login'
			// #ifndef H5
			tmpurl = 'login'
			// #endif
			uni.request({
				url: $C.getBaseUrl() + url + tmpurl,
				method: "POST",
				sslVerify: false,
				data: data,
				dataType: 'json', //设置json返回值就会json.parse
				header: {
					"Content-Type": `multipart/form-data; boundary=${boundary}`
				},
				success(res) {
					console.log(res);
					if (res.data.status == "0") {
						let token = res.data.data;

						if (!token || !token.startsWith("Bearer ")) {
							console.log("登陆异常,无效的token");
							uni.showToast({
								title: "登陆异常,无效token",
								icon: "none"
							})
							return;
						}
						try {
							let msg = res.data.msg;
							let isObject = msg && typeof JSON.parse(msg) == 'object';
							if (!isObject) {
								uni.showToast({
									title: "登陆异常",
									icon: "none"
								})
								return;
							}
						} catch (e) {
							console.log(e);
							uni.showToast({
								title: "登陆异常",
								icon: "none"
							})
							return;
						}
						console.log("登陆成功", res.data);

						uni.setStorageSync('token', token);
						// self.getUserInfo(JSON.parse(res.data.msg).userId);
						securityStorage.setStorageSync('user', res.data.msg);
						self.$H.refrenceToken();//开始调用定时刷新token
						uni.setStorageSync('user', res.data.msg);
						uni.showToast({
							title: "登录成功",
							icon: "none"
						})
						self.logining = false;
						isLoginSuccess = true;
						self.$H.href("/pages/index", 2);

					} else if (res.data.status == '3') {
						// console.log("登陆异常", res);
						uni.showToast({
							title: res.data.msg,
							icon: "none",
							duration: 4000
						});
						self.getCode();
					} else {
						console.log("登陆异常", res);
						uni.showToast({
							title: res.data.msg,
							icon: "none"
						})
					}
					self.logining = false;
				},
				fail: err => {
					uni.showModal({
						content: err.errMsg ? err.errMsg : "发生异常，请联络管理员"
					})
				},
			})
		},
		showMessage(checkRes) {
			uni.showToast({
				title: checkRes,
				icon: "none"
			});
		},
		checkAndCleanData() {
			let token = uni.getStorageSync('token');
			if (token) {
				uni.removeStorageSync('token');
				securityStorage.removeStorageSync('user');
			}
		},
		toSmsLogin() {
			uni.navigateTo({
				url: "./login_sms"
			})
		},
		href(page) {
			let url = "";
			switch (page) {
				case 1:
					url = "./verify_mobile"
					break;
				case 2:
					url = "./register"
					break;
				case 3:
					url = "./login_sms"
					break;
				default:
					break;
			}
			uni.navigateTo({
				url: url
			})
		},
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.content {
	position: relative;
	background: url('@/static/images_new/login/u2414.svg') no-repeat fixed;
	background-size: cover;
}

.tui-icon-close {
	margin: 0 auto
}

.bg-img {
	background: url('@/static/bg.png') no-repeat bottom left fixed;
	position: fixed;
	background-size: contain;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
}

.tui-login-way {
	width: 100%;
	font-size: 26rpx;
	color: $uni-text-color;
	display: flex;
	justify-content: center;
	position: fixed;
	left: 0;
	bottom: 180rpx;

	view {
		padding: 12rpx 10rpx;
	}
}

.tui-auth-login {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 80rpx;
	padding-top: 0rpx;

	.tui-icon-platform {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-left: 0rpx;

		&::after {
			content: '';
			position: absolute;
			width: 200%;
			height: 200%;
			transform-origin: 0 0;
			transform: scale(0.5, 0.5) translateZ(0);
			box-sizing: border-box;
			left: 0;
			top: 0;
			border-radius: 180rpx;
			border: 1rpx solid $uni-text-color-placeholder;
		}
	}

	.tui-login-logo {
		width: 60rpx;
		height: 60rpx;
	}
}

.tui-btn-send {
	width: 162rpx;
	height: 50rpx;
	text-align: center;
	padding-top: 10rpx;
	flex-shrink: 0;
	font-size: $uni-font-size-base;
	color: #ffffff;
	background: #95afc0; //
}

.tui-gray {
	background: #c09ee0;
	padding-top: 20rpx;
}

.image-code {
	width: 200rpx;
	height: 28px;
	margin-left: -200rpx;
}

:deep(.uni-easyinput__content){
    border-radius: 0 !important;
}
::v-deep .uni-easyinput-input {
	uni-view {
		border-radius: 0;
		height: 90rpx;
	}
}
</style>
