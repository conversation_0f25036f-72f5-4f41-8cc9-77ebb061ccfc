<template>
	<view :style="{ height: contentHeight + 'px' }" class="container">
		<view style="height:60rpx;"></view>
		<view style="display: flex;justify-content: center; align-items: center; flex-direction: column;">
			<text class="u-demo-block__title" style="margin-top: 25rpx;font-size: 60rpx; color: #fff;">移动运维管理平台</text>
			<text class="u-demo-block__title"
				style="margin-top: 25rpx;font-size: 32rpx;color: #fff;">随时随地访问，高效便捷运维</text>
		</view>

		<image style="
		    width: 376px;
			height: 388rpx;
			margin: 0 auto;
			display: flex;
			margin-top: 100rpx;
		" src="@/static/images_new/login/u2417.svg" mode="scaleToFill" />

		<uni-forms ref="loginForm" :rules="customRules" :modelValue="loginForm" style="padding: 40px;">
			<uni-forms-item name="account" style="margin: 0;">
				<uni-easyinput prefix-icon="phone" class="uni-easyinput-input phone-input" v-model="mobile"
					placeholder="请输入手机号">
				</uni-easyinput>
			</uni-forms-item>
			<!-- <uni-forms-item v-if="codeIco" name="verifyCode">
				<uni-easyinput prefix-icon="locked" v-model="picCode" placeholder="请输入图形码">
					<template #right>
						<image :src="'data:image/png;base64,' + codeIco" class="image-code" @click="getPicCode"></image>
					</template>
				</uni-easyinput>
			</uni-forms-item> -->
			<uni-forms-item name="checkPass">
				<uni-easyinput class="uni-easyinput-input" prefix-icon="locked" v-model="code" placeholder="请输入验证码">
					<template #right>
						<button plain size="mini" type="primary" shape="circle" style="transform: scale(0.8);"
							@click="getCode">
							{{ codeTime > 0 ? codeTime + ' s' : '获取验证码' }}
						</button>
					</template>
				</uni-easyinput>
			</uni-forms-item>
			<uni-forms-item>
				<button type="primary" style="margin-top: 40rpx; color: #fff;background-color: #0079fe;" shape="circle" :disabled="disabled" :loading="logining"
					@click="login">
					登录
				</button>
			</uni-forms-item>
			<uni-forms-item>
				<view style="text-align: center;">
					<text style="margin-top: 20px;color: #fff; font-weight: 550;" @click="toPwdLogin">
						使用账号密码登录
					</text>
				</view>
			</uni-forms-item>
		</uni-forms>

	</view>
</template>

<script>
import form from "@/common/validation.js"
import $C from '@/common/config.js'
import tuiButton from "@/components/tui/tui-button"
import tuiIcon from "@/components/tui/tui-icon"
import tuiListCell from "@/components/tui/tui-list-cell"
import securityStorage from '@/common/securityStorage'
import permissionService from '@/common/permission-service.js'
import globalState from '@/common/global-state.js'
let time = Date.parse(new Date()) / 1000
export default {
	computed: {
		disabled: function () {
			let bool = true;
			if (this.mobile && this.code && this.isAgree && this.picCode) {
				bool = false;
			}
			return bool;
		}
	},
	components: {
		tuiButton,
		tuiIcon,
		tuiListCell,
	},
	data() {
		return {
			navBarHeight: 0,    // 导航栏总高度（状态栏 + 标题栏）
			contentHeight: 0,   // 内容区域高度
			isAgree: true,
			agree: false,
			openid: '',
			successVal: 0,
			mobile: '',
			picCode: '',
			rnd: '',
			code: '',
			isSend: false,
			btnSendText: '获取验证码', //倒计时格式：(60秒)
			codeTime: 0,
			color: "#ffffff",
			background: "#a778d4",
			post_data: {},
			codeIco: "",
		};
	},
	onReady() {
		// 获取系统信息
		const systemInfo = uni.getSystemInfoSync()

		// 计算导航栏高度
		let statusBarHeight = systemInfo.statusBarHeight
		let titleBarHeight = 0

		// 平台差异处理
		// #ifdef MP-WEIXIN
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
		titleBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height
		// #endif
		// #ifndef MP-WEIXIN
		titleBarHeight = systemInfo.platform === 'ios' ? 44 : 48 // 默认值
		// #endif

		this.navBarHeight = statusBarHeight + titleBarHeight

		// 计算内容区域高度
		this.contentHeight = systemInfo.windowHeight;
	},
	onShow() {
		this.getPicCode();
	},
	methods: {
		clearInput(type) {
			if (type == 1) {
				this.mobile = '';
			}
		},
		toUserAgreement() {
			uni.navigateTo({
				url: "./agreement"
			})
		},
		toPrivacyAgreement() {
			uni.navigateTo({
				url: "./agreement_privacy"
			})
		},
		setAgree(e) {
			this.isAgree = !this.isAgree
			this.$emit('setAgree', this.isAgree)
		},
		check() {
			var rule = /^1[34578]\d{9}$/;
			if (!rule.test(this.mobile)) {
				uni.showToast({
					title: "手机号格式不正确",
					icon: "none"
				})
				return false;
			}
			if (this.picCode == '') {
				uni.showToast({
					title: "图形码不能为空",
					icon: "none"
				})
				return false;
			}
			return true
		},
		end() {
			this.color = '#4d2574';
		},
		// 获取图片验证码，短信防刷
		getPicCode() {
			let that = this
			// that.$H.get('/login/sms/getPicVerifyCodeForSendLoginSms').then(res => {
			// 	console.log("getCode", JSON.stringify(res));
			// 	if (res.status == "0") {
			// 		this.codeIco = res.data.image;
			// 		this.rnd = res.data.rnd;
			// 	}
			// })
		},
		// 获取验证码
		getCode() {
			let that = this
			// 防止重复获取
			if (that.codeTime > 0) {
				return;
			}
			// 验证手机号
			if (!that.check()) return;
			// 发送验证码
			uni.request({
				url: $C.getBaseUrl() + '/login/sms/sendVerifyCode',
				// url: 'http://10.1.22.124:8080/rest/login/sms/sendVerifyCode',
				method: "POST",
				sslVerify: false,
				data: {
					cellphone: that.mobile,
					rnd: that.rnd,
					imgVerifyCode: that.picCode,
				},
				dataType: 'json',//设置json返回值就会json.parse
				header: {
					"Content-Type": "application/x-www-form-urlencoded"
				},
				complete(res) {
					if (res.data.status == "0") {
						uni.showToast({
							title: '短信已发送',
							icon: 'none'
						});
						// 倒计时
						that.codeTime = 60
						let timer = setInterval(() => {
							if (that.codeTime >= 1) {
								that.codeTime--
							} else {
								that.codeTime = 0
								clearInterval(timer)
							}
						}, 1000)
						that.isSend = true;
					} else {
						console.log("sendVerifyCode", res.data);
						let msg = res.data.msg ? res.data.msg : "请查看手机短信";
						if (msg.includes("在本系统中未关联任何账号")) {
							msg = "用户名或密码错误";
						}
						uni.showToast({
							title: msg,
							icon: 'none',
							duration: 3000
						});
					}
				},
				fail(res) {
					uni.showModal({
						content: res.data.error ? res.data.error : "发生异常，请联络管理员"
					})
				},
			});
		},
		toPwdLogin() {
			uni.navigateTo({
				url: "./login_pwd"
			})
		},
		back() {
			uni.navigateBack();
		},
		inputPicCode(e) {
			this.picCode = e.detail.value;
		},
		inputCode(e) {
			this.code = e.detail.value;
		},
		inputMobile: function (e) {
			this.mobile = e.detail.value;
		},
		getUserInfo(userId) {
			this.$H.get('/framework/sysmanage/eam/user/getCurrentUserDetailInfo').then(res => {
				if (res.status == "0") {
					securityStorage.setStorageSync('user', res.data);
				}
			});
		},
		login() {
			let that = this

			// 登录前清除所有缓存，确保使用新用户的权限
			let token = uni.getStorageSync('token');
			if (token) {
				// 清除权限缓存
				permissionService.clearPermissionsAfterLogout();

				// 清除其他缓存
				uni.removeStorageSync('token');
				securityStorage.removeStorageSync('user');
				uni.removeStorageSync('config');

				console.log('登录前清除所有缓存，确保使用新用户的权限');
			}

			let come_from = that.$come_from
			let post_data = {
				from: come_from,
				cellphone: that.mobile,
				verifyCode: that.code,
				loginType: 3
			}
			console.log(post_data);
			//表单规则
			let rules = [{
				name: "cellphone",
				rule: ["required", "isMobile"],
				msg: ["请输入手机号", "请输入正确的手机号"]
			},
			{
				name: "verifyCode",
				rule: ["required", "isNum"],
				msg: ["请输入验证码", "请输入正确的验证码"]
			}
			];
			//进行表单检查
			let checkRes = form.validation(post_data, rules);
			if (!checkRes) {
				// 发送验证码
				uni.request({
					url: $C.getBaseUrl() + '/login/sms/doLogin',
					method: "POST",
					data: post_data,
					sslVerify: false,
					dataType: 'json',//设置json返回值就会json.parse
					header: {
						"Content-Type": "application/x-www-form-urlencoded"
					},
					// complete(res){
					// 	if(res.data.status == "Succeed"){
					// 		uni.setStorageSync('token', res.data.data);
					// 		that.getUserInfo(JSON.parse(res.data.msg).userId);
					// 		that.$H.href("/pages/login/start",1);
					// 		// setTimeout(() => {
					// 		// 	uni.showToast({
					// 		// 		title: "登录成功",
					// 		// 		icon: "none"
					// 		// 	})
					// 		// }, 1000);
					// 	}else{
					// 		uni.showToast({
					// 			title: res.data.msg,
					// 			icon: 'none',
					// 			duration: 3000
					// 		});
					// 	}
					// },
					// fail(res) {
					// 	uni.showModal({
					// 		content: err.errMsg ? err.errMsg : "发生异常，请联络管理员"
					// 	})
					// },
				}).then((res) => {
					if (res.data.status == "Succeed") {
						uni.setStorageSync('token', res.data.data);
						that.getUserInfo(JSON.parse(res.data.msg).userId);

						// 登录成功后立即加载权限 - 简化版本
						console.log('登录成功，强制更新权限');

						// 清除所有权限缓存
						permissionService.clearPermissionsAfterLogout();

						// 强制加载最新权限
						permissionService.loadUserPermissions(true)
							.then(() => {
								console.log('登录后权限强制更新成功');

								// 显示登录成功提示
								uni.showToast({
									title: "登录成功",
									icon: "none"
								});

								// 设置全局状态，标记需要刷新页面
								try {
									const userData = JSON.parse(res.data.msg);
									console.log('设置全局状态，标记需要刷新页面');
									globalState.setLoginSuccess(userData.userId);
								} catch (e) {
									console.error('解析用户数据失败:', e);
								}

								// 跳转到首页
								that.$H.href("/pages/index", 2);
							})
							.catch(err => {
								console.error('登录后权限更新失败:', err);

								// 即使权限加载失败，也允许用户登录
								uni.showToast({
									title: "登录成功",
									icon: "none"
								});

								// 设置全局状态，标记需要刷新页面
								try {
									const userData = JSON.parse(res.data.msg);
									console.log('设置全局状态，标记需要刷新页面');
									globalState.setLoginSuccess(userData.userId);
								} catch (e) {
									console.error('解析用户数据失败:', e);
								}

								// 跳转到首页
								that.$H.href("/pages/index", 2);
							});
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}).catch(err => {
					uni.showModal({
						content: err.errMsg ? err.errMsg : "发生异常，请联络管理员"
					})
				});
			} else {
				uni.showToast({
					title: checkRes,
					icon: "none"
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.bg-img {
	background: url('@/static/bg.png') no-repeat bottom left fixed;
	position: fixed;
	background-size: contain;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
}

.tui-login-way {
	width: 100%;
	font-size: 26rpx;
	// color: $uni-text-color;
	display: flex;
	justify-content: center;
	position: fixed;
	left: 0;
	bottom: 280rpx;

	view {
		padding: 12rpx 0;
	}
}

.tui-auth-login {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 80rpx;
	padding-top: 0rpx;

	.tui-icon-platform {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-left: 0rpx;

		&::after {
			content: '';
			position: absolute;
			width: 200%;
			height: 200%;
			transform-origin: 0 0;
			transform: scale(0.5, 0.5) translateZ(0);
			box-sizing: border-box;
			left: 0;
			top: 0;
			border-radius: 180rpx;
			border: 1rpx solid;
		}
	}

	.tui-login-logo {
		width: 60rpx;
		height: 60rpx;
	}
}

.image-code {
	width: 85px;
	height: 30px;
	margin-left: -80px;
}

.container {
	position: relative;
	background: url('@/static/images_new/login/u2414.svg') no-repeat fixed;
	background-size: cover;

	// 减去顶部导航标题高度
	.tui-page-title {
		width: 100%;
		font-size: 48rpx;
		font-weight: bold;
		//color: $uni-text-color;
		line-height: 42rpx;
		padding: 110rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
		text-align: center;
	}

	.tui-form {
		padding-top: 50rpx;

		.tui-view-input {
			width: 100%;
			box-sizing: border-box;
			padding: 0 60rpx;

			.tui-cell-input {
				width: 90%;
				display: flex;
				align-items: center;
				padding-top: 10rpx;
				//padding-bottom: $uni-spacing-col-base;

				input {
					flex: 1;
					//padding-left: $uni-spacing-row-base;
				}

				.tui-icon-close {
					margin-left: auto;
				}

				.tui-btn-send {
					width: 156rpx;
					height: 50rpx;
					text-align: center;
					padding-top: 10rpx;
					flex-shrink: 0;
					//font-size: $uni-font-size-base;
					color: #ffffff;
					background: #95afc0; //
				}

				.tui-gray {
					background: #95afc0;
				}
			}
		}

		.tui-cell-text {
			width: 100%;
			//padding: 40rpx $uni-spacing-row-lg;
			box-sizing: border-box;
			//font-size: $uni-font-size-sm;
			//color: $uni-text-color-grey;
			display: flex;
			align-items: center;

			.tui-color-primary {
				margin-left: -10px;
				color: #4d2574;
				// padding-left: $uni-spacing-row-sm;
			}
		}

		.tui-btn-box {
			width: 100%;
			// padding: 0 $uni-spacing-row-lg;
			box-sizing: border-box;
			margin-top: 80rpx;
		}
	}
}

::v-deep .uni-easyinput-input {
	uni-view {
		border-radius: 0;
		height: 90rpx;
	}
}

::v-deep .uni-easyinput__content-prefix {
	background: url('@/static/images_new/login/u2433.svg');
	background-size: contain;
	background-repeat: no-repeat;
	width: 20px;
	height: 20px;
}
</style>
