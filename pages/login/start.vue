<template>
	<view>
<!-- 		<el-form>
			<el-form-item label="用户名">
				<el-select>
					<el-option label="测试1" value="1"></el-option>
					<el-option label="测试2" value="2"></el-option>
				</el-select>
			</el-form-item>
		</el-form> -->
<!--		<image class="bg-img" :showLoading="true"/>-->
		
<!--		<u-transition mode="slide-left" :show="show1" :custom-style="style1">  -->
<!--			<view class="title1">如果一切都井井有条</view>-->
<!--		</u-transition>-->
		<u-transition mode="fade" :show="show3">  
			<view class="button-group">
				<view class="title2">安管平台</view>
				<button @click="go" class="next" type="default">欢迎使用</button>
			</view>
		</u-transition>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				style1: {
					position: "fixed",
					top: `${uni.$u.sys().windowHeight / 2 + 90}px`,
					left: `${uni.$u.sys().windowWidth / 2 - 45}px`,
					width: "120px",
					height: "120px",
					backgroundColor: "#b4d4fb",
				},
				show1: false,
				show3: false,
				time: null,
				count: '',
			}
		},
		onLoad() {
			this.openTransition();
		},
		methods: {
			go() {
				uni.switchTab({
					url: '/pages/index'
				})
			},
			openTransition() {
				setTimeout(() => {
					this.show1 = true;
				}, 500);
				setTimeout(() => {
					this.show2 = true;
				}, 2000);
				setTimeout(() => {
					this.show3 = true;
				}, 2000);
			},
		}
	}
</script>

<style>
	.bg-img {
		background: url('@/static/sky2.png') no-repeat top right fixed;
		position: absolute;
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		top: 0;
		left: 0;
		z-index: -1;
		/* object-fit: contain; */
	}
	
	.title1 {
	    color: #ffffff;
		font-style:italic;
		font-weight:bold;
		font-size: 23px;
		width: 250px;
	}
	
	.title2 {
	    color: #6272ce;
		/* font-family:Verdana,Genva,Arial,sans-serif; */
		font-weight: bold;
		font-size: 23px;
		margin-left: 20px;
		margin-bottom: 20px;
		width: 200px;
	}

	.content {
		display: flex;
		flex-direction: column;
		font-size: 28rpx;
	}

	.words {
		padding: 0 26rpx;
		line-height: 46rpx;
		margin-top: 20rpx;
		margin-bottom: 80px;
	}

	.button-group {
		display: flex;
		flex-direction: column;
		position: fixed;
		height: 150px;
		bottom: 55px;
		width: 750rpx;
		justify-content: center;
		align-items: center;
		border-top: solid 0px #f2f4f9;
		padding-top: 5px;
		background-color: #b4d4fb;
	}

	.button-group button {
		border-radius: 100px;
		border: none;
		width: 300rpx;
		height: 42px;
		line-height: 42px;
		font-size: 32rpx;
	}

	.button-group button:after {
		border: none;
	}

	.button-group button.next {
		color: #566778; 
		background: #ffffff;
		border: solid 1px #ffffff;
	}
</style>
