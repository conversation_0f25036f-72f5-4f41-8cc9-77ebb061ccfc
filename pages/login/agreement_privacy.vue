<template>
	<view class="content">
		<view class="title-group">
			<text style="text-align: center;font-weight: bolder;font-size: 15px;">中国移动共享能力维护中心隐私协议</text>
		</view>
		<u-gap height="30"></u-gap>
		<text class="words" space="emsp">
			用户隐私协议
			
			  尊敬的用户，欢迎使用由中国移动共享能力维护中心（下列简称为“共享运维”或“共享运维信息”）提供的服务。在使用前请您阅读如下服务协议，使用本应用即表示您同意接受本协议，本协议产生法律效力，特别涉及免除或者限制共享运维责任的条款，请仔细阅读。如有任何问题，可向共享运维信息咨询。
			
			1. 服务条款的确认和接受
			
			通过访问或使用本应用，表示用户同意接受本协议的所有条件和条款。
			
			2. 服务条款的变更和修改
			
			共享运维信息有权在必要时修改服务条款，服务条款一旦发生变更，将会在重要页面上提示修改内容。如果不同意所改动的内容，用户可以放弃获得的本应用信息服务。如果用户继续享用本应用的信息服务，则视为接受服务条款的变更。本应用保留随时修改或中断服务而不需要通知用户的权利。本应用行使修改或中断服务的权利，不需对用户或第三方负责。
			
			3. 用户行为
			
			3.1 用户账号、密码和安全
			
			您无需注册登录账号即可使用我司产品功能。
			
			3.2 用户在本应用上不得发布下列违法信息和照片：
			
			（1）反对宪法所确定的基本原则的；
			
			（2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；
			
			（3）损害国家荣誉和利益的；
			
			（4）煽动民族仇恨、民族歧视，破坏民族团结的；
			
			（5）破坏国家宗教政策，宣扬邪教和封建迷信的；
			
			（6）散布谣言，扰乱社会秩序，破坏社会稳定的；
			
			（7）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；
			
			（8）侮辱或者诽谤他人，侵害他人合法权益的；
			
			（9）含有法律、行政法规禁止的其他内容的；
			
			（10）禁止骚扰、毁谤、威胁、仿冒网站其他用户；
			
			（11）严禁煽动非法集会、结社、游行、示威、聚众扰乱社会秩序；
			
			（12）严禁发布可能会妨害第三方权益的文件或者信息，例如（包括但不限于）：病毒代码、黑客程序、软件破解注册信息。
			
			（13）禁止上传他人作品。其中包括你从互联网上下载、截图或收集的他人的作品；
			
			（14）禁止上传广告、横幅、标志等网络图片；
			
			4. 服务内容
			
			本服务的具体内容由共享运维信息根据实际情况提供，共享运维信息可以对其提供的服务予以变更，且共享运维信息提供的服务内容可能随时变更。该软件属于免费应用，用户使用过程中，本软件不会以任何方式、名义收取任何费用。
			
			隐私条款
			
			4.1 用户信息公开情况说明
			
			尊重用户个人隐私是共享运维信息的一项基本政策。所以，共享运维不会在未经合法用户授权时公开、编辑或透露其注册资料及保存在本应用中的非公开内容，除非有下列情况：
			
			（1）有关法律规定或共享运维合法服务程序规定；
			
			（2）在紧急情况下，为维护用户及公众的权益；
			
			（3）为维护共享运维的商标权、专利权及其他任何合法权益；
			
			（4）其他需要公开、编辑或透露个人信息的情况；
			
			 在以下（包括但不限于）几种情况下，共享运维信息有权使用用户的个人信息：
					 
			（1）与国家安全、国防安全直接相关的；
			
			（2）与公共安全、公共卫生、重大公共利益直接相关的；
			
			（3）与犯罪侦查、起诉、审判和判决执行等直接相关的；
			
			（4）出于维护您或其他个人的生命、财产等重大合法权益但又很难得到您本人同意的；
			
			4.2.隐私权政策适用范围
			
			（1）用户在使用本应用服务器时留下的个人身份信息；
			
			（2）用户通过本应用服务器与其他用户或非用户之间传送的各种资讯；
			
			（3）本应用与商业伙伴共享的其他用户或非用户的各种信息；
			
			（4）共享运维信息软件十分注重保护用户的个人隐私，并制定了《隐私政策》，用户亦可以通过《隐私政策》，用户确认并同意使用共享运维信息软件提供的服务将被视为接受《隐私政策》。
			
			4.3、我们如何收集和使用您的个人信息
			
			个人信息是指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映	特定自然人活动情况的各种信息。
			
			本公司仅会出于本政策所述的以下目的，收集和使用您的个人信息: 
			
			(一) 为更好地为您提供服务，本公司将遵循“合法、正当、必要”原则，按照法律法规的规定和您的同意收集您的个人信息，主要包括: 
			
			1.您提供的信息 
			
			您注册成为我们的用户或使用我们的服务时，向我们提供的相关个人信息，包括您的账号名称、头像、电话号码、电子邮箱地址等。
			
			您提供的上述信息，将在您使用服务期间持续授权我们使用。在您注销账号时，我们将删除您的个人信息或做匿名化处理。 
			
			2.您在使用服务过程中产生的信息 
			
			您使用我们提供的服务时，我们可能自动收集以下信息:
			
			(1)日志信息
			
			指您使用我们服务时，系统可能通过cookies、web beacon 或其他方式自动采集的技术信息，包括: 
			
			A.设备或软件信息，例如您的移动设备、网页浏览器或您用于接入我们的服务的其他程序所提供的配置信息、您的IP地址和您的移动设备所用的版本和设备识别码; 
			
			B.您在使用我们服务时搜索和浏览的信息，例如您使用的搜索关键字、访问页面，以及您在使用我们服务时浏览或要求提供的其他信息;
			
		3.我们如何保护和保存您的个人信息
			
			（一）我们如何保护您的个人信息
			
			1. 我们会严格按照《网络安全法》、《全国人民代表大会常务委员会关于加强网络信息保护的决定》、《电信和互联网用户个人信息保护规定》（工业和信息化部令第24号）、《电话用户真实身份信息登记规定》（工业和信息化部令第25号）等法律法规的相关要求，建立信息安全保障制度，采取技术措施和其他必要措施保护您的个人信息安全。
			
			2. 我们只会在达成本政策所述目的所需的期限内保留您的个人信息，除非需要延长保留期或受到法律的允许。
			
			3. 请您理解：互联网环境并非百分之百安全，我们将尽力确保或担保您发送给我们的任何信息的安全性，但由于技术的限制及可能存在的各种恶意手段，即便竭尽所能加强安全措施，也不可能始终保证信息百分之百安全。在不幸发生个人信息安全事件后，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。我们将及时将事件相关情况以邮件、信函、电话、推送通知等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。同时，我们还将按照监管部门要求，主动上报个人信息安全事件的处置情况。
			
			（二）我们如何保存您的个人信息
			
			1. 信息存储的地点
			  
			  我们会按照法律法规规定，将境内收集的用户个人信息存储于中国境内，不存在境外收集用户信息情况。
			
			2. 信息存储的期限
		    
			  我们承诺您个人信息的存储时间始终处于合理必要期限内，我们根据相关规定及业务需要时长去存储用户信息，超出保存期限的用户信息，我们会做匿名化处理；当我们的产品或服务发生停止运营的情形时，我们将以推送通知、公告形式通知您，并在合理的期限内删除您的个人信息或进行匿名化处理。
			  
			  注：法律法规有规定不能删除时或匿名化处理的情况除外
			
		（三）个人信息安全保护措施和能力
			
			 我们会采用符合业界标准的安全防护措施，包括建立合理的制度规范、安全技术来防止您的个人信息遭到未经授权的访问使用、修改，免数据的损坏或丢失。
			
			 手机APP的网络服务采取了传输层安全协议等加密技术，通过https等方式提供浏览服务，确保用户数据在传输过程中的安全。
			
			 手机APP采取加密技术对用户个人信息进行加密保存，并通过隔离技术进行隔离。
			
			 在个人信息使用时，我们会采用包括内容替换、SHA256在内多种数据脱敏技术增强个人信息在使用中安全性。
			
			 手机APP采用严格的数据访问权限控制和多重身份认证技术保护个人信息，避免数据被违规使用。
			
			 手机APP采用代码安全自动检查、数据访问日志分析技术进行个人信息安全审计。
						
			4.4资讯公开与共享
			
			共享运维信息不会将用户的个人信息和资讯故意透露、出租或出售给任何第三方。但以下情况除外：
			
			（1）用户本人同意与第三方共享信息和资讯;
			
			（2）只有透露用户的个人信息和资讯，才能提供用户所要求的某种产品和服务;
			
			（3）应代表本应用提供产品或服务的主体的要求提供（除非我们另行通知，否则该等主体无权将相关用户个人信息和资讯用于提供产品和服务之外的其他用途）：根据法律法规或行政命令的要求提供;因外部审计需要而提供;用户违反了本应用服务条款或任何其他产品及服务的使用规定;经本站评估，用户的帐户存在风险，需要加以保护。
			
			4.5 非个人隐私信息
			
			为了改善共享运维信息软件的技术和服务，向用户提供更好的服务体验，共享运维信息或可会自行收集使用或向第三方提供用户的非个人隐私信息。
			
			如何联系我们
			
			如果您对本个人信息保护政策有任何疑问、意见或建议，通过以下方式与我们联系：
			<EMAIL>
			
			一般情况下，我们将在7个工作日内回复。
			
			如果您对我们的回复不满意，特别是您认为我们的个人信息处理行为损害了您的合法权益，您还可以依法向人民法院提起诉讼。
			
			产品运营者基本情况:
			产品名称：中国移动政企共享能力维护中心APP客户端
			公司名称：中国移动通信团公司政企事业部
			注册地址：北京市西城区金融大街29号
			个人信息保护联系人：<EMAIL>


		</text>
		<view class="button-group">
			<button v-if="count > 0" @click="cancel" type="warn" disabled="true">阅读协议 {{count}}s</button>
			<button v-else @click="cancel" type="warn">我已同意</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				time: null,
				count: '',
			}
		},
		onReachBottom() {
			console.log("触底模式");
		},
		onLoad() {
			this.getCode();
		},
		methods: {
			getCode() {
				const TIME_COUNT = 8;
				if (!this.timer) {
					this.count = TIME_COUNT;
					this.timer = setInterval(() => {
						if (this.count > 0 && this.count <= TIME_COUNT) {
							this.count--;
						} else {
							clearInterval(this.timer);
							this.timer = null;
						}
					}, 1000)
				}
			},
			cancel() {
				uni.navigateBack()
			},
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		font-size: 28rpx;
	}

	.words {
		padding: 0 26rpx;
		line-height: 46rpx;
		margin-top: 20rpx;
		margin-bottom: 80px;
	}

	.title-group {
		display: flex;
		flex-direction: row;
		position: fixed;
		height: 70px;
		top: 10px;
		width: 750rpx;
		justify-content: center;
		align-items: center;
		border-top: solid 1px #e4e6ec;
		padding-top: 5px;
		background-color: #FFFFFF;
	}

	.button-group {
		display: flex;
		flex-direction: row;
		position: fixed;
		height: 70px;
		bottom: 0px;
		width: 750rpx;
		justify-content: center;
		align-items: center;
		border-top: solid 1px #e4e6ec;
		padding-top: 5px;
		background-color: #FFFFFF;
	}

	.button-group button {
		border-radius: 100px;
		border: none;
		width: 300rpx;
		height: 42px;
		line-height: 42px;
		font-size: 32rpx;
	}

	.button-group button:after {
		border: none;
	}

	.button-group button.next {
		color: #e64340;
		border: solid 1px #e64340;
	}
</style>
