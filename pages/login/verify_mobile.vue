<template>
	<view class="container">
		<view class="tui-page-title">找回密码</view>
		<view class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="mobile" color="#4d2574" :size="20"></tui-icon>
						<input :value="mobile" placeholder="请输入手机号" placeholder-class="tui-phcolor" type="number"
							maxlength="11" @input="inputMobile" />
						<view class="tui-icon-close" v-show="mobile" @tap="clearInput(1)">
							<tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon>
						</view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="shield" color="#4d2574" :size="20"></tui-icon>
						<input placeholder="请输入图形码" placeholder-class="tui-phcolor" type="text" maxlength="6"
							@input="inputPicCode" />
						<image :src="'data:image/png;base64,' + codeIco" class="image-code" @click="getPicCode"></image>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="shield" color="#4d2574" :size="20"></tui-icon>
						<input placeholder="请输入验证码" placeholder-class="tui-phcolor" type="text" maxlength="6"
							@input="inputCode" />
						<view class="tui-btn-send" :class="{ 'tui-gray': isSend }"
							:hover-class="isSend ? '' : 'tui-opcity'" :hover-stay-time="2" @click="getCode">
							<text>{{codeTime > 0 ? codeTime + ' s' : btnSendText}}</text>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-btn-box">
				<tui-button :disabledGray="true" :disabled="disabled" :shadow="true" shape="circle" @tap="verify">验证手机
				</tui-button>
			</view>
			<view class="tui-cell-text">

			</view>
		</view>
	</view>
</template>

<script>
	import form from "@/common/validation.js"
	let time = Date.parse(new Date()) / 1000
	import $C from '@/common/config.js'
	import tuiButton from "@/components/tui/tui-button"
	import tuiIcon from "@/components/tui/tui-icon"
	import tuiListCell from "@/components/tui/tui-list-cell"
	import tuiCountdownVerify from "@/components/tui/tui-countdown-verify"
	export default {
		computed: {
			disabled: function() {
				let bool = true;
				if (this.mobile && this.code && this.picCode) {
					bool = false;
				}
				return bool;
			}
		},
		components: {
			tuiButton,
			tuiIcon,
			tuiListCell,
			tuiCountdownVerify
		},
		data() {
			return {
				successVal: 0,
				mobile: '',
				code: '',
				isSend: false,
				btnSendText: '获取验证码', //倒计时格式：(60秒)
				codeTime: 0,
				color: "#ffffff",
				background: "#a778d4",
				picCode: "",
				codeIco: "",
				rnd: '',
			};
		},
		onShow() {
			this.getPicCode();
		},
		methods: {
			check() {
				var rule = /^1[34578]\d{9}$/;
				if (!rule.test(this.mobile)) {
					uni.showToast({
						title: "手机号格式不正确",
						icon: "none"
					})
					return false;
				}
				if (this.picCode == '') {
					uni.showToast({
						title: "图形码不能为空",
						icon: "none"
					})
					return false;
				}
				return true
			},
			end() {
				this.color = '#4d2574';
			},
			// 获取图片验证码，短信防刷
			getPicCode() {
				let that = this

				that.$H.get('/login/retrieve/pwd/getPicVerifyCodeForSendResetPwdSms').then(res => {
					console.log("getCode", JSON.stringify(res));
					if (res.status == "0") {
						that.codeIco = res.data.image;
						that.rnd = res.data.rnd;
					}
				})
			},
			// 获取验证码
			getCode() {
				let that = this
				// 防止重复获取
				if (that.codeTime > 0) {
					return;
				}
				// 验证手机号
				if (!that.check()) return;
				// 发送验证码
				uni.request({
					url: $C.getBaseUrl() + '/login/retrieve/pwd/sendSmsVerifyCode',
					method:"POST",
					sslVerify: false,
					data:{
						cellPhone: that.mobile,
						rnd: that.rnd,
						imgVerifyCode: that.picCode,
					},
					dataType:'json',//设置json返回值就会json.parse
					header:{
						"Content-Type":"application/x-www-form-urlencoded",
						"Authorization":uni.getStorageSync("token"),
					},
					complete(res){
						if(res.data.status == "0"){
							uni.showToast({
								title: '短信已发送',
								icon: 'none'
							});
							// 倒计时
							that.codeTime = 60
							let timer = setInterval(() => {
								if (that.codeTime >= 1) {
									that.codeTime--
								} else {
									that.codeTime = 0
									clearInterval(timer)
								}
							}, 1000)
							that.isSend = true;
						}else{
							console.log("sendBindCellPhoneVerifyCode",res.data);
							uni.showToast({
								title: res.data.msg ? res.data.msg : "请查看手机短信",
								icon: 'none',
								duration: 3000
							});
						}
					},
					fail(res) {
						uni.showModal({
							content: res.data.error ? res.data.error : "发生异常，请联络管理员"
						})
					},
				});
			},
			back() {
				uni.navigateBack();
			},
			inputPicCode(e) {
				this.picCode = e.detail.value;
			},
			inputCode(e) {
				this.code = e.detail.value;
			},
			inputMobile: function(e) {
				this.mobile = e.detail.value;
			},
			clearInput(type) {
				if (type == 1) {
					this.mobile = '';
				} else if (type == 2)
					this.password = '';
				else {
					this.password2 = '';
				}
			},
			verify() {
				let that = this
				let post_data = {
					cellPhone: that.mobile,
					rnd: that.rnd,
					imgVerifyCode: that.picCode,
				}
				//表单规则
				let rules = [{
						name: "cellPhone",
						rule: ["required", "isMobile"],
						msg: ["请输入手机号", "请输入正确的手机号"]
					}
				];
				//进行表单检查
				let checkRes = form.validation(post_data, rules);
				if (!checkRes) {
					post_data.from = that.$come_from
					uni.request({
						url: $C.getBaseUrl() + '/login/retrieve/pwd/resetPwdBySmsCode',
						method:"POST",
						data:post_data,
						sslVerify: false,
						dataType:'json',//设置json返回值就会json.parse
						header:{
							"Content-Type":"application/x-www-form-urlencoded",
							"Authorization":uni.getStorageSync("token"),
						},
						complete(res){
							console.log(res);
							if(res.data.status == "0"){
								uni.showToast({
									title: '请查阅手机短信',
									icon: 'none',
									duration: 3000
								});
							}else{
								console.log("error",res.data);
								uni.showToast({
									title: res.data.msg ? res.data.msg : "请查看手机短信",
									icon: 'none',
									duration: 3000
								});
							}
						},
						fail(res) {
							uni.showModal({
								content: res.data.error ? res.data.error : "发生异常，请联络管理员"
							})
						},
					});
					
					
					// that.$H.post('/login/retrieve/pwd/sendSmsVerifyCode', post_data).then(res => {
					// 	if (res.status == 200 && res.data.token) {
					// 		uni.setStorageSync('token', res.data.token);
					// 		let arr = {}
					// 		arr['data'] = res.data.my
					// 		arr['save_time'] = time
					// 		uni.setStorageSync('my', arr);
					// 		uni.navigateTo({
					// 			url: '/pages/login/reset_pwd'
					// 		})
					// 		setTimeout(() => {
					// 			uni.showToast({
					// 				title: "验证成功",
					// 				icon: "none"
					// 			})
					// 		}, 1000);
					// 	} else if (res.status == 400) {
					// 		setTimeout(() => {}, 2000);
					// 		uni.showToast({
					// 			title: res.msg,
					// 			icon: "none"
					// 		})
					// 	}
					// })
				} else {
					uni.showToast({
						title: checkRes,
						icon: "none"
					});
				}
			},
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		.image-code {
			width: 97px;
			height: 30px;
			margin-left: -80px;
		}

		.tui-page-title {
			width: 100%;
			font-size: 48rpx;
			font-weight: bold;
			//color: $uni-text-color;
			line-height: 42rpx;
			padding: 110rpx 40rpx 40rpx 40rpx;
			box-sizing: border-box;
			text-align: center;
		}

		.tui-form {
			padding-top: 50rpx;

			.tui-view-input {
				width: 100%;
				box-sizing: border-box;
				padding: 0 40rpx;

				.tui-cell-input {
					width: 100%;
					display: flex;
					align-items: center;
					padding-top: 10rpx;
					//padding-bottom: $uni-spacing-col-base;

					input {
						flex: 1;
						//padding-left: $uni-spacing-row-base;
					}

					.tui-icon-close {
						margin-left: auto;
					}

					.tui-btn-send {
						width: 180rpx;
						height: 50rpx;
						text-align: center;
						padding-top: 10rpx;
						flex-shrink: 0;
						//font-size: $uni-font-size-base;
						color: #ffffff;
						background: #95afc0; //
					}

					.tui-gray {
						//color: $uni-text-color-placeholder;
						background: #c09ee0;
						padding-top: 20rpx;
					}
				}
			}

			.tui-cell-text {
				width: 100%;
				//padding: 40rpx $uni-spacing-row-lg;
				box-sizing: border-box;
				//font-size: $uni-font-size-sm;
				//color: $uni-text-color-grey;
				display: flex;
				align-items: center;

				.tui-color-primary {
					color: #4d2574;
					//padding-left: $uni-spacing-row-sm;
				}
			}

			.tui-btn-box {
				width: 100%;
				//padding: 0 $uni-spacing-row-lg;
				box-sizing: border-box;
				margin-top: 80rpx;
			}
		}
	}
</style>
