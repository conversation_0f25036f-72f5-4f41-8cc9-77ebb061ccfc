<template>
	<view class="container">
		<view class="tui-page-title">注册</view>
		<view class="tui-form">
			<view class="tui-view-input">
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="mobile" color="#4d2574" :size="20"></tui-icon>
						<input :value="mobile" placeholder="请输入手机号" placeholder-class="tui-phcolor" type="number" maxlength="11" @input="inputMobile" />
						<view class="tui-icon-close" v-show="mobile" @tap="clearInput(1)"><tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon></view>
					</view>
				</tui-list-cell>
				
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#4d2574" :size="20"></tui-icon>
						<input :value="password" placeholder="请输入六位及以上的密码" :password="true" placeholder-class="tui-phcolor" type="text" maxlength="40" @input="inputPwd" />
						<view class="tui-icon-close" v-show="password" @tap="clearInput(2)"><tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon></view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="pwd" color="#4d2574" :size="20"></tui-icon>
						<input :value="password2" placeholder="请再次确认密码" :password="true" placeholder-class="tui-phcolor" type="text" maxlength="40" @input="inputPwd2" />
						<view class="tui-icon-close" v-show="password2" @tap="clearInput(3)"><tui-icon name="close-fill" :size="16" color="#bfbfbf"></tui-icon></view>
					</view>
				</tui-list-cell>
				<tui-list-cell :hover="false" :lineLeft="false" backgroundColor="transparent">
					<view class="tui-cell-input">
						<tui-icon name="shield" color="#4d2574" :size="20"></tui-icon>
						<input placeholder="请输入验证码" placeholder-class="tui-phcolor" type="text" maxlength="6" @input="inputCode" />
						<view class="tui-btn-send" :class="{ 'tui-gray': isSend }" :hover-class="isSend ? '' : 'tui-opcity'" :hover-stay-time="2" @click="getCode">
						<text>{{codeTime > 0 ? codeTime + ' s' : btnSendText}}</text>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-btn-box"><tui-button :disabledGray="true" :disabled="disabled" :shadow="true" shape="circle" @tap="reg">注册</tui-button></view>
			<view class="tui-cell-text">
				注册代表同意
				<view class="tui-color-primary" hover-class="tui-opcity" :hover-stay-time="2" @tap="protocol">用户服务协议、隐私政策</view>
			</view>
		</view>
	</view>
</template>

<script>
import form from "@/common/validation.js"
import tuiButton from "@/components/tui/tui-button"
import tuiIcon from "@/components/tui/tui-icon"
import tuiListCell from "@/components/tui/tui-list-cell"	
import tuiCountdownVerify from "@/components/tui/tui-countdown-verify"	
let time = Date.parse(new Date()) / 1000
export default {
	computed: {
		disabled: function() {
			let bool = true;
			if (this.mobile && this.code && this.password) {
				bool = false;
			}
			return bool;
		}
	},
	components:{
		tuiButton,
		tuiIcon,
		tuiListCell,
		tuiCountdownVerify
	},
	data() {
		return {
			openid:'',
			successVal: 0,
			mobile: '',
			password: '',
			password2: '',
			code: '',
			isSend: false,
			btnSendText: '获取验证码' ,//倒计时格式：(60秒)
			codeTime:0,
			color:"#ffffff",
			background:"#a778d4",
			post_data:{}
		};
	},
	methods: {
		check(){
			var rule = /^1[34578]\d{9}$/; 
			if (!rule.test(this.mobile)) {
				uni.showToast({
					title:"手机号格式不正确",
					icon:"none"
				})
				return false;
			}
			return true
		},
		
		end() {
			this.color = '#4d2574';
		},
		
		// 获取验证码
		getCode(){
			let that = this
			// 防止重复获取
			if (this.codeTime > 0) {
				return;
			}
			// 验证手机号
			if (!this.check()) return;
			// 请求数据
			
			this.$H.post('sapi/mobile/sendSms',{mobile:this.mobile}).then(res => { 
					
					if(res.data>0){
						uni.showToast({
							title: '短信已发送',
							icon: 'none'
						});
					}
					
						
				})
				
			uni.login({
				provider: 'weixin',
				success: function(res) {
					uni.request({
						url: that.$C.api_url + 'sapi/auth/getMiniAppOpenId',
						method: 'POST',
						data: {
							code: res.code
						},
						success: function(res) {
						
							that.openid = res.data.openid
							
							
						}
					})
				}
			})
			// 倒计时
			this.codeTime = 60
			let timer = setInterval(()=>{
				if (this.codeTime >= 1) {
					this.codeTime--
				} else {
					this.codeTime = 0
					clearInterval(timer)
				}
			},1000)
			this.isSend = true;
		},
		back() {
			uni.navigateBack();
		},
		inputCode(e) {
			this.code = e.detail.value;
			console.log(this.code)
		},
		inputMobile: function(e) {
			this.mobile = e.detail.value;
			console.log(this.mobile)
		},
		inputPwd: function(e) {
			this.password = e.detail.value;
			console.log(this.password)
		},
		inputPwd2: function(e) {
			this.password2 = e.detail.value;
			console.log(this.password2)
		},
		clearInput(type) {
			if (type == 1) {
				this.mobile = '';
			} else if(type == 2)
			   this.password = '';
			else {
				this.password2 = '';
			}
		},
		reg(){
			console.log('ss')
			let that = this
			let come_from = this.$come_from
			let post_data = {from:come_from,pwd:that.password,pwd2:that.password2,mobile:that.mobile,vericode:that.code}
			that.post_data = {from:come_from,pwd:that.password,pwd2:that.password2,mobile:that.mobile,vericode:that.code}
			
			//表单规则
			let rules = [ {
				name: "mobile",
				rule: ["required", "isMobile"],
				msg: ["请输入手机号", "请输入正确的手机号"]
			},
			{
				name: "pwd",
				rule: ["required", "isEnAndNo"],
				msg: ["请输入密码", "密码为6~20位数字和字母组合"]
			}, {
				name: "pwd2",
				rule: ["required", "isSame:pwd"],
				msg: ["请输入确认密码", "两次输入的密码不一致"]
			},
			{
				name: "vericode",
				rule: ["required", "isNum"],
				msg: ["请输入验证码", "请输入正确的验证码"]
			}];
			//进行表单检查
			let checkRes = form.validation(that.post_data, rules);
			if (!checkRes) {
				uni.showToast({
					title: "验证通过!",
					icon: "none"
				});
			
				switch (come_from) {
				  case 'miniapp':
				  that.miniApp()
				    break;
				  case 'app':
				    that.app()
				    break;
				  case 'h5':
				    
				    break;
				  
				}
				
			} else {
				uni.showToast({
					title: checkRes,
					icon: "none"
				});
			}
			
			
		},
		regAction(){
			let that = this
			let post_data = that.post_data
			that.$H.post('api/auth/reg',post_data).then(res => {
				
				if(res.status == 200 && res.data.token){
					setTimeout(() => {
						uni.showToast({
							title:"注册成功",
							icon:"none"
						})
						
					}, 1000);
					uni.setStorageSync('token', res.data.token);
					let arr={}
					arr['data'] = res.data.my
					arr['save_time']=time
					uni.setStorageSync('my', arr);
					uni.switchTab({
						url: '/pages/user/user'
					})
				}else{
					setTimeout(() => {
						uni.showToast({
							title:res.msg,
							icon:"none"
						})
						
					}, 1000);
				}
					
						
				})
		},
		miniApp(){
			let that = this
			uni.login({
				provider: 'weixin',
				success: function(res) {
					that.$H.post('sapi/auth/getMiniAppOpenId',{code: res.code}).then(res2 => {
						
						that.post_data.openid = res2.openid
						
						that.regAction()
						})
					
				}
			})
		},
		app(){
			let that = this
			uni.login({
			  provider: 'weixin',
			  success: function (loginRes) { 
			    // 获取用户信息
			    uni.getUserInfo({
			      provider: 'weixin', 
				  success: data => {
					  uni.showLoading({
						title:"登录中..."
					  })
					console.log(data.userInfo); 
				  	if(data.errMsg=="getUserInfo:ok"){
						that.post_data.openid = data.userInfo.openId
						that.regAction()
					}
				  }
			    });
			  }
			});
		},
		
		protocol(){
			this.tui.href("/pages/doc/protocol/protocol")
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	.tui-page-title {
		width: 100%;
		font-size: 48rpx;
		font-weight: bold;
		//color: $uni-text-color;
		line-height: 42rpx;
		padding: 110rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
		text-align: center;
	}
	.tui-form {
		padding-top: 50rpx;
		.tui-view-input {
			width: 100%;
			box-sizing: border-box;
			padding: 0 40rpx;
			.tui-cell-input {
				width: 100%;
				display: flex;
				align-items: center;
				padding-top: 10rpx;
				//padding-bottom: $uni-spacing-col-base;
				input {
					flex: 1;
					//padding-left: $uni-spacing-row-base;
				}
				.tui-icon-close {
					margin-left: auto;
				}
				.tui-btn-send {
					width: 158rpx;
					height: 50rpx;
					text-align: center;
					padding-top: 10rpx;
					flex-shrink: 0;
					//font-size: $uni-font-size-base;
					color: #ffffff;
					background: #95afc0;//
				}
				.tui-gray {
					//color: $uni-text-color-placeholder;
					background: #c09ee0;
					padding-top: 20rpx;
				}
			}
		}
		.tui-cell-text {
			width: 100%;
			//padding: 40rpx $uni-spacing-row-lg;
			box-sizing: border-box;
			//font-size: $uni-font-size-sm;
			//color: $uni-text-color-grey;
			display: flex;
			align-items: center;
			.tui-color-primary {
				color: #4d2574;
				//padding-left: $uni-spacing-row-sm;
			}
		}
		.tui-btn-box {
			width: 100%;
			//padding: 0 $uni-spacing-row-lg;
			box-sizing: border-box;
			margin-top: 80rpx;
		}
	}
}
</style>
