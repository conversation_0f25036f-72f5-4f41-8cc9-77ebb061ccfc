<template>
	<!-- 添加header插槽 -->
	<slot name="header"></slot>

	<view class="segmented-control-container uni-padding-wrap">
		<uni-segmented-control :current="current" :values="items" :style-type="styleType" :active-color="activeColor"
			@clickItem="onClickItem" />
	</view>

	<view class="content-container">
		<slot></slot>
	</view>


</template>

<script>
	export default {
		props: {
			tabs: {
				type: Array,
				default: () => []
			}
		},
		computed: {

		},
		mounted() {
			uni.getSystemInfo().then(res => {
				this.currentBodyHeight = res.windowHeight - 50 + 'px'
			})
		},
		data() {
			return {
				currentBodyHeight:0,
				items: ['最新消息', '历史消息'],
				styles: [{
						value: 'button',
						text: '按钮',
						checked: true
					},
					{
						value: 'text',
						text: '文字'
					}
				],
				colors: ['#007aff', '#4cd964', '#dd524d'],
				current: 0,
				colorIndex: 0,
				activeColor: '#007aff',
				styleType: 'text'

			}
		},
		methods: {
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex
				}
			},
			styleChange(e) {
				if (this.styleType !== e.detail.value) {
					this.styleType = e.detail.value
				}
			},
			colorChange(e) {
				if (this.styleType !== e.detail.value) {
					this.activeColor = e.detail.value
				}
			}
		},
		watch:{
			current:{
				handler(val){
					this.$emit("getActivateValue",val)
				},
				deep:true,
				immediate:true
			}
		}

	}
</script>
<style lang="scss" scoped>
	$tabsHeight: 50px;

	.example-body {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 0;
	}

	.uni-common-mt {
		// margin-top: 30px;
		background: #FFFFFF;
	}

	.uni-padding-wrap {
		// width: 750rpx;
		// padding: 0px 30px;
		font-size: 36rpx;
		background: #FFFFFF;
	}

	.content {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		height: 150px;
		text-align: center;
	}

	.content-text {
		font-size: 14px;
		color: #666;
	}

	.color-tag {
		width: 25px;
		height: 25px;
	}

	.uni-list {
		flex: 1;
	}

	.uni-list-item {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: row;
		background-color: #FFFFFF;
	}


	.uni-list-item__container {
		padding: 12px 15px;
		width: 100%;
		flex: 1;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		box-sizing: border-box;
		/* #endif */
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-bottom-style: solid;
		border-bottom-width: 1px;
		border-bottom-color: #eee;
	}

	.uni-list-item__content-title {
		font-size: 14px;
	}

	.segmented-control-container {
		position: fixed;
		width: 100vw;
		left: 0;
		height: 53px;
		z-index: 998;
		/* #ifdef H5 */
		top: 104px; /* H5端：导航栏高度(44px) + 搜索栏高度(60px) */
		/* #endif */
		/* #ifndef H5 */
		top: 60px; /* APP端：搜索栏高度 */
		/* #endif */
	}

	.content-container {
		/* #ifdef H5 */
		padding-top: 100px; /* H5端：导航栏高度(44px) + 搜索栏高度(60px) + 分段控制器高度(53px) + 额外空间 */
		/* #endif */
		/* #ifndef H5 */
		padding-top: 100px; /* APP端：搜索栏高度(60px) + 分段控制器高度(53px) + 额外空间 */
		/* #endif */
	}
</style>