<template>
	<view class="items">
		<view @click="bindClick" threshold="0" :right-options="rightOptions">
			<uni-card :is-shadow="true" is-full spacing="0px" margin="0px" padding="10px">
				<view>
					<uni-row class="asset-column" style="color: #3C5176; font-size: .98em;">
						<uni-col :span="9" style="width: 80px;">
							<view class="label">提交时间：</view>
						</uni-col>
						<uni-col :span="15" style="width: calc(100% - 80px);">
							<view class="value" style="float: left;">
								{{contant.createTime}}
							</view>
							<view style="float: right;">
								<uni-tag size="mini" text="高" v-if="contant.priority == 0" type="danger"></uni-tag>
								<uni-tag size="mini" text="中" v-if="contant.priority == 1" type="warning"></uni-tag>
								<uni-tag size="mini" text="低" v-if="contant.priority == 2"></uni-tag>
							</view>
						</uni-col>
					</uni-row>
				</view>

				<view class="split-line"></view>

				<view class="items_head">
					<view class="message_title">
						<view class="circle_orange"></view>
						<text class="contant">
							{{contant.typeName}}
						</text>
					</view>
					<!-- <view class="message_type">
						安全
					</view> -->
					<!-- <view class="operate_type">未处置</view> -->
				</view>
				<view class="message_contant">
					{{contant.messageTitle}}
				</view>

			</uni-card>
		</view>

	</view>
</template>

<script>
	import {
		reactive
	} from "vue"
	export default {
		props: ["itemContant"],
		emits: ["getSwipeActionId"],
		setup(props,context) {
			const {emit}=context;
			let contant = reactive(props.itemContant)
			let rightOptions = reactive([{
					text: '标记已读',
					style: {
						backgroundColor: '#007aff'
					}
				},
				// {
				// 	text: '删除',
				// 	style: {
				// 		width:'200px',
				// 		backgroundColor: '#F56C6C'
				// 	}
				// }
			])
			function bindClick(e){
				emit("getSwipeActionId",contant.messageId)
			}
			
			return {
				rightOptions,
				contant,
				bindClick
			}
		}

	}
</script>

<style lang="scss" scoped>
	.items {
		margin-bottom: 5pt;

		&_head {
			width: 100%;
			align-items: center;

			.message_title {
				display: flex;
				align-items: center;
				float: left;
				max-width: 50%;
				font-size: 13pt;
				font-weight: 600;
				color: #10172A;

				.circle_orange {
					display: inline-block;
					height: 8pt;
					width: 8pt;
					border-radius: 50%;
					background: #ff6c19;
					margin-bottom: 8pt;
				}

				.contant {
					display: inline-block;
					width: 100%;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					margin-left: 5pt;
					margin-bottom: 8pt;
				}
			}

			.message_type {
				float: left;
				display: inline-block;
				width: 50px;
				text-align: center;
				background: url("../assets/images/Rectangle_safety.png") no-repeat;
				background-size: 100% 100%;
				font-size: 11pt;
				font-weight: bold;
				color: #FFF;
			}

			.operate_type {
				float: right;
				width: 20%;
				text-align: right;
				color: #CC4519;
				font-size: 12pt;
				font-weight: bold;
			}

		}

		.message_contant {
			font-size: 12pt;
			color: #3C5176;
			width: 100%;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}

		.message_time {
			font-size: 12pt;
			color: #3C5176;
		}
	}

	.split-line {
		height: 1px;
		background-color: #E3E8F0;
		transform: scaleY(.7);
		margin: 8px 0;
	}
</style>