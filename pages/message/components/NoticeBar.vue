<template>
	<view>
		<view class="notice-box" :style="'background-color: ' + bgColor + ';'">
			<view class="notice-icon">
				消息提醒
				<!-- <uni-icons :color="color" type="sound" size="24"></uni-icons> -->
			</view>
			<scroll-view class="notice-content">
				<uni-row>
					<swiper v-if="list.length > 0" class="swiper" :autoplay="true" :interval="switchTime * 1000"
						:duration="1500" :circular="true" :vertical="true">
						<swiper-item v-for="(item, index) in list" :key="index" class="notice-content-item ">
							<uni-col @click="handleGetMoreMessage(item)" :span="20">
								<text class=" notice-content-item-text" :style="'color: ' + color + ';'">
									{{ item.messageTitle }}
								</text>
							</uni-col>

							<uni-col :span="4">
								<text class="notice-content-item-todetail">
									<uni-icons @click="handleGetMoreMessage(item)" :color="color" size="24"
										type="arrowright" />
								</text>
							</uni-col>


						</swiper-item>
					</swiper>
					<div v-else @click="toMessageManage" style="line-height: 60rpx;color: #2a88f1;">暂无未读消息</div>
				</uni-row>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		list: {
			type: Array,
			default: ['暂无未读消息']
		},
		color: {
			type: String,
			default: '#2a88f1'
		},
		bgColor: {
			type: String,
			default: '#fff'
		},
		switchTime: {
			type: Number,
			default: 3
		},
		border: {
			type: String,
			default: '1px solid #eee'
		}
	},
	methods: {
		handleGetMoreMessage(row) {
			uni.navigateTo({
				url: "/pages/message/detail_message?messageID=" + row.messageId + `&relationId=${row.relationId}`
			})
		},
		toMessageManage() {
			uni.navigateTo({
				url: "/pages/message/view_message"
			})
		},
	}
}
</script>

<style>
.swiper {
	height: 60upx !important;
}

.notice-box {
	border: 1px solid #ffffff51;
	border-radius: 6px;
	
	width: 100%;
	height: 60upx;
	/* padding: 0 10upx; */
	overflow: hidden;
	/* margin: 20upx 0; */
	display: flex;
	justify-content: flex-start;
}

.notice-icon {
	width: 260upx;
	height: 60upx;
	line-height: 60upx;
	text-align: center;
	position: relative;
	background: url("../assets/images/Rectangle.png") no-repeat;
	background-size: 100% 100%;
	color: aliceblue;
	font-weight: bold;
	text-shadow: rgba(255, 255, 255, 0.5) 0 5px 6px, rgba(255, 255, 255, 0.2) 1px 3px 3px;
}

.notice-content {
	width: 100%;
	position: relative;
	font-size: 14px;
}

.notice-content-item {
	width: 50%;
	height: 60upx;
	text-align: left;
	line-height: 60upx;
	display: flex;
	justify-content: space-between;
}

.notice-content-item-text {
	display: block;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.notice-content-item-todetail {
	display: block;
	text-align: right;
}

@keyframes anotice {
	0% {
		transform: translateY(100%);
	}

	30% {
		transform: translateY(0);
	}

	70% {
		transform: translateY(0);
	}

	100% {
		transform: translateY(-100%);
	}
}
</style>