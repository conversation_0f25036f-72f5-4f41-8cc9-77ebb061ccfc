import axios from "/common/axios.js"
import $C from "/common/config.js"
/***
门户消息 接口
*/

const mockData = false
const basePath = "/dap/message"

export default {
	// 轮播放消息
	getMessageRingData() {
		let result
		if (mockData) {
			result = new Promise(function(res, rej) {
				res({
					"status": "consequat",
					"msg": "labore",
					"data": {
						"needDoingData": [{
								"messageTitle": "采或铁定接律",
								"moduleId": "29",
								"typeId": "60",
								"typeName": "根机军京自",
								"createTime": "1993-06-17 11:39:45",
								"status": false,
								"priority": 12,
								"relationId": "56",
								"messageId": "39",
								"moduleName": "报几近"
							},
							{
								"status": true,
								"messageId": "93",
								"moduleId": "11",
								"priority": 7,
								"moduleName": "六际所再从",
								"messageTitle": "新克风全",
								"relationId": "13",
								"createTime": "1995-10-31 12:05:01",
								"typeName": "文委律众号但",
								"typeId": "83"
							},
							{
								"messageTitle": "关需局元",
								"moduleId": "53",
								"priority": 80,
								"createTime": "2020-12-10 07:52:50",
								"typeId": "93",
								"messageId": "55",
								"typeName": "维许风",
								"status": false,
								"moduleName": "度斗集大",
								"relationId": "90"
							}
						],
						"noticeData": [{
								"typeId": "75",
								"relationId": "60",
								"messageId": "35",
								"typeName": "件江问包",
								"status": false,
								"moduleName": "会连设",
								"createTime": "1974-11-17 18:46:02",
								"moduleId": "13",
								"priority": 65,
								"messageTitle": "领带市事展"
							},
							{
								"messageId": "50",
								"relationId": "47",
								"typeName": "类验科整必",
								"messageTitle": "八太特联",
								"createTime": "2000-03-07 07:15:15",
								"priority": 66,
								"moduleName": "前使候件如",
								"status": false,
								"moduleId": "77",
								"typeId": "48"
							},
							{
								"typeId": "6",
								"priority": 28,
								"createTime": "1976-03-02 10:24:31",
								"moduleName": "下无识处化",
								"moduleId": "55",
								"typeName": "要革县合为",
								"relationId": "92",
								"messageId": "61",
								"messageTitle": "手引主温",
								"status": false
							},
							{
								"moduleId": "33",
								"messageId": "79",
								"priority": 77,
								"typeName": "么前发传理家",
								"messageTitle": "克资流天听",
								"relationId": "78",
								"status": true,
								"moduleName": "许想强题",
								"createTime": "1987-08-14 00:32:37",
								"typeId": "5"
							},
							{
								"priority": 43,
								"createTime": "2020-01-22 19:36:19",
								"typeId": "80",
								"messageId": "85",
								"typeName": "部间安了被构表",
								"moduleName": "空省机里更",
								"relationId": "71",
								"messageTitle": "类是二划代",
								"moduleId": "91",
								"status": true
							}
						]
					}
				})
			})
		} else {
			result = axios.get($C.portalContextPathNew + "/message/manage/getMessageRingData")
		}
		return result
	},
	// 未读消息
	getCountUserUnReadMessage() {
		let result
		if (mockData) {
			result = new Promise(function(res, rej) {
				res({
					"status": "0",
					"msg": "恭喜！操作成功！",
					"data": {
						"total": "100",
						"needDeal": "99",
						"notice": "1"
					}
				})
			})
		} else {
			result = axios.get($C.portalContextPathNew + "/message/manage/countUserUnReadMessage")
		}
		return result
	},
	// 消息详情
	getMessageInfo(params) {
		let result
		if (mockData) {
			result = new Promise(function(res, rej) {
				res({
					"msg": "ullamco reprehenderit",
					"status": "magna Lorem est labore",
					"data": {
						"typeName": "照精我况",
						"messageId": "10",
						"moduleName": "天道容局",
						"createTime": "2000-12-21 19:17:21",
						"messageContent": "ea",
						"messageTitle": "别越化要把管作",
						"priority": 56,
						"linkData": [{
								"linkText": "Ut non",
								"routeName": "日响持次图",
								"outLinkUrl": "http://dxpm.中国互联.公司/fptwdx",
								"params": {
									"key": "ex commodo"
								}
							},
							{
								"outLinkUrl": "http://lkevoa.fm/uxxyc",
								"routeName": "拉新眼",
								"linkText": "veniam",
								"params": {
									"key": "anim"
								}
							},
							{
								"params": {
									"key": "eu mollit quis consequat"
								},
								"linkText": "aliquip reprehenderit",
								"outLinkUrl": "http://tsphu.km/whz",
								"routeName": "飞老段内干"
							},
							{
								"linkText": "adipisicing ipsum",
								"outLinkUrl": "http://pycj.ge/ekrmpmcqub",
								"params": {
									"key": "sunt"
								},
								"routeName": "上红次"
							}
						],
						"contentType": 96
					}
				})
			})
		} else {
			result = axios.get($C.portalContextPathNew + "/message/manage/getMessageInfo", params)
		}
		return result
	},
	//新-消息读取接口
	getUpdateReadStatus(relationIds){
		return axios.get($C.portalContextPathNew + `/message/manage/updateReadStatus?relationIds=${relationIds}`)
	},
	//一键已读
	 oneKeyRead(params){
		 return axios.get($C.portalContextPathNew + "/message/manage/oneKeyRead",{subject:0})
	 },
	//待办通知
	getMessagePageData(params){
		return axios.get($C.portalContextPathNew + "/message/manage/getMessageRingData",params)
	},
	//获取已读-未读接口
	manageGetMessagePageData(params){
		return axios.post($C.portalContextPathNew + "/message/manage/getMessagePageData",{
			...params
		})
	},
	// 批量转换状态 st//portal-eam/dap/message/manage/updateReadStatus?relationIds=dDVqs4s8FPPK1a9NDqh
	updateReadStatus(params){
		return axios.get($C.portalContextPathNew + `/message/manage/getMessageInfo?messageId=${params.relationIds}`)
	},
}