<template>
	<view class="messageDetail" :class="{ 'messageDetail-dark': detail.theme }">
		<!-- 技术变更单消息使用简化布局 -->
		<template v-if="detail.typeName === '技术变更单消息'">
			<view class="tech-change-message">
				<view class="tech-title">
					<text>标题：{{ detail.messageTitle }}</text>
				</view>
				<view class="tech-content">
					<text class="content-label">正 文：</text>
					<view class="content-text">
						<text>您于</text>
						<text class="time-link" @click="goToTodoList">{{ detail.createTime }}</text>
						<text>收到一条待办工单，请到待办事项中处理！</text>
					</view>
				</view>
			</view>
		</template>

		<!-- 其他类型消息使用原有详细布局 -->
		<template v-else>
			<view class="message-info">
				<view class="info-item">
					<text class="label">标题：</text>
					<text class="value">{{ detail.messageTitle }}</text>
				</view>
				<view class="info-item">
					<text class="label">通知编号：</text>
					<text class="value">{{ detail.messageId }}</text>
				</view>
				<view class="info-item">
					<text class="label">创建人：</text>
					<text class="value">{{ detail.creatorName }}</text>
				</view>
				<view class="info-item">
					<text class="label">创建部门：</text>
					<text class="value">{{ detail.deptName }}</text>
				</view>
				<view class="info-item">
					<text class="label">消息级别：</text>
					<text class="value">
						<text class="priority-tag" :class="getPriorityClass(detail.priority)">{{ getPriorityText(detail.priority) }}</text>
					</text>
				</view>
				<view class="info-item">
					<text class="label">提交时间：</text>
					<text class="value">{{ detail.createTime }}</text>
				</view>
				<view class="info-item">
					<text class="label">正 文：</text>
					<text class="value">{{ detail.messageContent }}</text>
				</view>
				<view class="info-item" v-if="detail.linkData">
					<text class="label">附 件：</text>
					<view class="value">
						<!-- 附件列表，如果有的话 -->
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
import {
	ref,
	reactive,
	onMounted,
	watch
} from "vue"
import {
	onLoad
} from "@dcloudio/uni-app"
import messageData from "../message/api/messageData.js"
import { onShow } from '@dcloudio/uni-app';
export default {

	setup() {
		const theme = ref(false);
		let detail = reactive({
			messageId: "",
			messageTitle: "",
			moduleName: "",
			typeName: "",
			priority: 0,
			messageContent: "",
			contentType: 0,
			createTime: "",
			creatorId: "",
			creatorName: "",
			deptId: "",
			deptName: "",
			linkData: null,
			relationId: "",
			theme: false,
		})

		// 处理消息级别的方法
		const getPriorityText = (priority) => {
			switch(priority) {
				case 0:
					return "一般";
				case 1:
					return "重要";
				case 2:
					return "紧急";
				default:
					return "一般";
			}
		}

		// 获取消息级别对应的样式类
		const getPriorityClass = (priority) => {
			switch(priority) {
				case 0:
					return "priority-normal";
				case 1:
					return "priority-important";
				case 2:
					return "priority-urgent";
				default:
					return "priority-normal";
			}
		}

		onLoad(function (options) {
			detail.messageId = options.messageID || options.messageId
			detail.relationId = options.relationId
		})

		watch(
			() => detail.theme,
			(newVal) => {
				uni.setStorageSync('theme', newVal);
				if (newVal) {
					uni.setNavigationBarColor({
						frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
						backgroundColor: '#2b2b2b', // 背景颜色
						// animation: { duration: 100 } // 过渡动画
					});
					uni.setTabBarStyle({
						backgroundColor: '#2b2b2b',
						color: '#ffffff',
						selectedColor: '#fff'
					});
				} else {
					uni.setNavigationBarColor({
						frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
						backgroundColor: '#ffffff', // 背景颜色
						// animation: { duration: 100 } // 过渡动画
					});
					uni.setTabBarStyle({
						backgroundColor: '#ffffff',
						color: '#000000',
						selectedColor: '#000'
					});
				}
			}
		);

		onShow(() => {
			detail.theme = uni.getStorageSync('theme') || false;
			if (detail.theme) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		})


		onMounted(() => {
			let params = {
				messageId: detail.messageId,
				relationId: detail.relationId
			}
			messageData.getMessageInfo(params).then(res => {
				if (res.status === "0" && res.data) {
					// 更新所有字段
					detail.messageTitle = res.data.messageTitle || "";
					detail.moduleName = res.data.moduleName || "";
					detail.typeName = res.data.typeName || "";
					detail.priority = res.data.priority || 0;
					detail.messageContent = res.data.messageContent || "";
					detail.contentType = res.data.contentType || 0;
					detail.createTime = res.data.createTime || "";
					detail.creatorId = res.data.creatorId || "";
					detail.creatorName = res.data.creatorName || "";
					detail.deptId = res.data.deptId || "";
					detail.deptName = res.data.deptName || "";
					detail.linkData = res.data.linkData;
				}
			})
			messageData.getUpdateReadStatus(params.relationId);
			theme.value = uni.getStorageSync('theme') || false;
			console.log("uni.getStorageSync('theme')",theme.value);
			if (theme.value) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		})

		let handleNum = () => {
			detail.num++
		}

		// 跳转到待办列表页面
		const goToTodoList = () => {
			uni.navigateTo({
				url: '/pages/list/my_list?index=0'
			});
		}

		return {
			detail,
			handleNum,
			getPriorityText,
			getPriorityClass,
			goToTodoList
		}
	}
}
</script>

<style lang="scss" scoped>
page {
	background: #fff;
}

.messageDetail {
	background-color: #fff;
	min-height: 100vh;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;

	/* 技术变更单消息样式 */
	.tech-change-message {
		padding: 20rpx;

		.tech-title {
			padding-bottom: 20rpx;
			font-size: 30rpx;

			text {
				word-break: break-all;
				line-height: 1.6;
			}
		}

		.tech-content {
			margin-top: 20rpx;
			display: flex;

			.content-label {
				color: #333;
				margin-right: 10rpx;
			}

			.content-text {
				flex: 1;
				line-height: 1.6;
				color: #333;

				.time-link {
					color: #2196F3;
				}
			}
		}
	}

	.message-info {
		background-color: #fff;
		padding: 20rpx;
		border-radius: 10rpx;

		.info-item {
			display: flex;
			margin-bottom: 20rpx;
			line-height: 1.5;

			.label {
				color: #999;
				width: 180rpx;
				flex-shrink: 0;
			}

			.value {
				flex: 1;
				color: #333;
				word-break: break-all;
				line-height: 1.6;
			}

			.priority-tag {
				display: inline-block;
				padding: 4rpx 16rpx;
				border-radius: 6rpx;
				font-size: 24rpx;
			}

			.priority-normal {
				background-color: #2db7f5;
				color: #fff;
			}

			.priority-important {
				background-color: #ff9900;
				color: #fff;
			}

			.priority-urgent {
				background-color: #ff4d4f;
				color: #fff;
			}
		}
	}

	/* 移除了单独的正文和附件样式，使用统一的info-item样式 */
}

.messageDetail-dark {
	background-color: #2b2b2b !important;
	color: #fff !important;

	/* 技术变更单消息深色模式样式 */
	.tech-change-message {
		.tech-title {
			text {
				color: #fff !important;
			}
		}

		.tech-content {
			.content-label {
				color: #bbb !important;
			}

			.content-text {
				color: #fff !important;

				.time-link {
					color: #4dabf5 !important;
				}
			}
		}
	}

	.message-info {
		background-color: #333 !important;

		.info-item {
			.label {
				color: #bbb !important;
			}

			.value {
				color: #fff !important;
			}
		}
	}

	/* 移除了单独的正文和附件深色模式样式，使用统一的info-item深色模式样式 */
}
</style>