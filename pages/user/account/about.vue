<template>
	<!-- <view class="about"> -->
		<view class="content">
			<!-- <view class="qrcode">
				<image src="@/static/images/ewm.png" @longtap="save"></image>
				<text class="tip">扫码下载</text>
			</view>
			<view class="desc">
				【安管平台】手机端APP，是针对资产、工单、消息的快捷解决方案。
			</view> -->
			
			<view
				style="margin: 0 auto; margin-top: 66rpx;  display: flex;justify-content: center; align-items: center; flex-direction: column;">
				<text class="u-demo-block__title"
					style="font-size: 60rpx; color: #000;font-weight: 500;">移动运维管理平台</text>
				<text class="u-demo-block__title"
					style="margin-top: 30rpx;font-size: 32rpx;color: #000;">随时随地访问，高效便捷运维</text>
			</view>

			<image style="
					width: 376px;
					height: 388rpx;
					margin: 0 auto;
					display: inline-block;
					margin-top: 100rpx;
				" src="@/static/images_new/login/u2417.svg" mode="scaleToFill" />
			<view style="margin: 0 auto;width: 90%;" class="list">
				<uni-list>
					<uni-list-item class="item" title="服务协议" show-arrow clickable @click="toUserAgreement">
					</uni-list-item>
					<uni-list-item class="item" title="当前版本" rightText="1.0.0">
					</uni-list-item>
					<!-- 					<uni-list-item class="item" title="版本更新" show-arrow clickable @click="updateVer()" link>
					</uni-list-item> -->
				</uni-list>
				<!-- <view class="tui-mtop">
					<tui-list-cell padding="0" :lineLeft="false" :arrow="true" @click="href(3)">
						<view class="tui-list-cell">
							分享给好友
						</view>
					</tui-list-cell>
				</view> -->
			</view>
			<!-- 			<text class="agreement" @click="toUserAgreement">《中国移动共享能力运维用户服务协议》</text>
			<text class="agreement" @click="toPrivacyAgreement">《中国移动共享能力运维隐私政策》</text> -->
			<!-- <text class="kefu">客服及投拆电话：<text class="code">95588</text></text> -->
			<!-- #ifdef APP-PLUS -->
			<!-- <button type="primary" @click="share">分享</button> -->
			<!-- #endif -->
		</view>

		<!-- #ifdef APP-PLUS -->
		<!-- <view class="version">
			当前版本：{{version}}
		</view> -->
		<!-- #endif -->

	<!-- </view> -->
</template>

<script>
import tuiListCell from '@/components/tui/tui-list-cell';
import tuiButton from "@/components/tui/tui-button"
export default {
	components: {
		tuiListCell,
		tuiButton
	},
	data() {
		return {
			providerList: [],
			version: ''
		}
	},
	onShow() {
		this.$H.checkLoginAndJumpStart();//检查登录状态
	},
	onLoad() {
		// #ifdef APP-PLUS
		// this.version = '0.0.7';//plus.runtime.version;
		// uni.getProvider({
		// 	service: 'share',
		// 	success: (result) => {
		// 		const data = [];
		// 		for (let i = 0; i < result.provider.length; i++) {
		// 			switch (result.provider[i]) {
		// 				case 'weixin':
		// 					data.push({
		// 						name: '分享到微信好友',
		// 						id: 'weixin'
		// 					});
		// 					data.push({
		// 						name: '分享到微信朋友圈',
		// 						id: 'weixin',
		// 						type: 'WXSenceTimeline'
		// 					});
		// 					break;
		// 				case 'qq':
		// 					data.push({
		// 						name: '分享到QQ',
		// 						id: 'qq'
		// 					});
		// 					break;
		// 				default:
		// 					break;
		// 			}
		// 		}
		// 		this.providerList = data;
		// 	},
		// 	fail: (error) => {
		// 		console.log('获取分享通道失败' + JSON.stringify(error));
		// 	}
		// });
		// #endif
	},
	methods: {
		updateVer() {
			setTimeout(() => {
				uni.showToast({
					title: "当前为最新版本",
					icon: "none"
				})
			}, 1000);
		},
		toUserAgreement() {
			this.$H.href("/pages/login/agreement", 1);
		},
		toPrivacyAgreement() {
			this.$H.href("/pages/login/agreement_privacy", 1);
		},
		// #ifdef APP-PLUS
		save() {
			uni.showActionSheet({
				itemList: ['保存图片到相册'],
				success: () => {
					plus.gallery.save('https://img.cdn.aliyun.dcloud.net.cn/guide/uniapp/app_download.png', function () {
						uni.showToast({
							title: '保存成功',
							icon: 'none'
						});
					}, function () {
						uni.showToast({
							title: '保存失败，请重试！',
							icon: 'none'
						});
					});
				}
			});
		},
		share(e) {
			if (this.providerList.length === 0) {
				uni.showModal({
					title: '当前环境无分享渠道!',
					showCancel: false
				});
				return;
			}
			let itemList = this.providerList.map(function (value) {
				return value.name;
			})
			uni.showActionSheet({
				itemList: itemList,
				success: (res) => {
					let provider = this.providerList[res.tapIndex].id;
					uni.share({
						provider: provider,
						scene: this.providerList[res.tapIndex].type && this.providerList[res.tapIndex].type === 'WXSenceTimeline' ?
							'WXSenceTimeline' : "WXSceneSession",
						type: (provider === "qq") ? 1 : 0,
						title: '欢迎体验uni-app',
						summary: 'uni-app 是一个使用 Vue.js 开发跨平台应用的前端框架',
						imageUrl: 'https://vkceyugu.cdn.bspapp.com/VKCEYUGU-dc-site/b09e38e0-5168-11eb-b680-7980c8a877b8.jpg',
						href: "https://m3w.cn/uniapp",
						success: (res) => {
							console.log("success:" + JSON.stringify(res));
						},
						fail: (e) => {
							uni.showModal({
								content: e.errMsg,
								showCancel: false
							})
						}
					});
				}
			})
		}
		// #endif
	}
}
</script>

<style>
.tui-userinfo-box {
	margin: 20rpx 0;
	color: #333;
}

.tui-list-cell {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 60rpx 24rpx 30rpx;
	box-sizing: border-box;
	font-size: 30rpx;
}

page,
view {
	/* display: flex; */
}

page {
	min-height: 100%;
	background-color: #FFFFFF;
}

image {
	width: 360rpx;
	height: 360rpx;
}

.about {
	flex-direction: row;
	flex: 1;
}

.agreement {
	font-size: 14px;
	margin-top: 10px;
	color: #007AFF;
	display: flex;
	justify-content: center;
}

.kefu {
	font-size: 14px;
	margin-top: 10px;
	color: #3b3a39;
	display: flex;
	justify-content: center;
}

.content {
	/* flex: 1; */
	/* padding: 40rpx; */
	flex-direction: column;
	justify-content: center;
	box-sizing: border-box;
}

.list {
	/* flex: 1; */
	padding: 50rpx 20rpx 50rpx 20rpx;
	flex-direction: column;
	justify-content: center;
}

.qrcode {
	display: flex;
	align-items: center;
	flex-direction: column;
}

.qrcode .tip {
	margin-top: 20rpx;
	font-size: 16px;
}

.desc {
	margin-top: 30rpx;
	display: block;
	font-size: 15px;
	color: green;
}

.code {
	color: #e96900;
	background-color: #f8f8f8;
}

button {
	width: 100%;
	margin-top: 20rpx;
}

.version {
	height: 80rpx;
	line-height: 80rpx;
	justify-content: center;
	color: #ccc;
}

.source {
	margin-top: 30rpx;
	flex-direction: column;
}

.source-list {
	flex-direction: column;
}

.link {
	color: #007AFF;
}
</style>
