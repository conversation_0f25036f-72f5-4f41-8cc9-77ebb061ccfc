<template>
	<view class="tui-userinfo-box">

		<uni-list>
			<!-- <uni-list-item title="重置密码" clickable show-arrow @click="reSetPwd"></uni-list-item>
			<uni-list-item v-if="userInfo.手机号" title="已绑定手机号" clickable show-arrow @click="bindTel"></uni-list-item>
			<uni-list-item v-else title="绑定手机号" clickable show-arrow @click="bindTel"></uni-list-item> -->
			<uni-list-item title="关于" clickable show-arrow @click="about"></uni-list-item>
			<uni-list-item title="退出登录" clickable show-arrow @click="logOut"></uni-list-item>
		</uni-list>

		<!-- <tui-list-cell padding="0" :lineLeft="false" :arrow="true" @click="reSetPwd">
			<view class="tui-list-cell">
				重置密码
			</view>
		</tui-list-cell>
		<tui-list-cell padding="0" :arrow="true" unlined @click="bindTel">
			<view class="tui-list-cell" v-if="userInfo.手机号">
				<view>已绑定手机号</view>
				<view class="tui-content">{{userInfo.手机号}}</view>
			</view>
			<view class="tui-list-cell" v-else>
				<view>绑定手机号</view>
				<view class="tui-content"></view>
			</view>
		</tui-list-cell>
		<tui-list-cell padding="0" :lineLeft="false" :arrow="true" @click="about">
			<view class="tui-list-cell">
				关于
			</view>
		</tui-list-cell>
		<tui-list-cell padding="0" :lineLeft="false" :arrow="true" @click="logOut">
			<view class="tui-list-cell">
				退出登录
			</view>
		</tui-list-cell> -->
	</view>
</template>

<script>
	import securityStorage from '@/common/securityStorage'
	import permissionService from '@/common/permission-service.js'
	export default {
		components: {
		},
		data() {
			return {
				userInfo: [],
			}
		},
		onLoad() {
		},
		onShow() {
			this.$H.checkLoginAndJumpStart();//检查登录状态
			let token = uni.getStorageSync('token');
			let user = securityStorage.getStorageSync('user');
			console.log("user =================== ", user);
			if (user && token) {
				this.userInfo = user;
			};
		},
		methods: {
			about() {
				uni.navigateTo({
					url: '/pages/user/account/about'
				})
			},
			logOut() {
				let token = uni.getStorageSync('token');
				if (token) {
					// 清除权限缓存
					permissionService.clearPermissionsAfterLogout();

					// 清除其他缓存
					uni.removeStorageSync('token');
					securityStorage.removeStorageSync('user');
					uni.removeStorageSync('config');

					console.log('用户登出，已清除权限缓存和其他缓存');

					// this.userInfo = []
					// // #ifdef MP-WEIXIN || APP-PLUS
					// uni.navigateTo({
					// 	url: '/pages/login/login_pwd'
					// })
					// // #endif
					// // #ifdef H5
					// var domine = window.location.href.split("?code")[0]
					// console.log('url:') + domine
					// window.location.href = domine + '#/pages/login/login_pwd'
					// // #endif
				}
				uni.redirectTo({
					url: '/pages/login/login_pwd'
				})
			},
			reSetPwd() {
				uni.navigateTo({
					url: "/pages/login/reset_pwd"
				});
			},
			bindTel() {
				uni.navigateTo({
					url: "/pages/user/account/bindTel?phone="+this.userInfo.手机号,
				});
			},
		},
		bindTel() {
			this.$U.href("./bindTel")
		},
		getUserInfo: function() {
			let that = this
			that.$H.post('sapi/uinfo/info', that.form).then(res => {
				if (res.status == 200 && res.data) {
					console.log('res.data:' + JSON.stringify(res.data))
					that.userInfo = res.data;
				} else if (res.status == 400) {
					setTimeout(() => {}, 2000);
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
			})
		},

	}
</script>

<style>
	.tui-userinfo-box {
		margin: 20rpx 0;
		color: #333;
	}
</style>
