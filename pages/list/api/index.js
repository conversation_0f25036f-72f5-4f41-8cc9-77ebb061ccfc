import axios from "/common/axios.js"
import config from "/common/config.js"
/**
 * 获取所有待办
 */
export const queryTodoList = (params) => {

	// return new Promise((resolve, reject) => {
	// 	setTimeout(() => {
	// 		resolve({
	// 			data: {
	// 				total: 10,
	// 				list: Array.from({length: 10}, (_, index) => {
	// 					return {
	// 						title: '模拟标题——' + index,
	// 						no: index + 1,
	// 						user: "曹雪阳",
	// 						dw: "东莞市政务服务数据管理局",
	// 						sj: "2023-05-19 10:27:19"
	// 					}
	// 				})
	// 			}
	// 		})
	// 	}, 200);
	// })
	return axios.post("/flow/task/listToDoPage", params);
}

/**
 * 获取所有已办
 */
export const queryDoneList = (params) => {
	return axios.post("/flow/task/listDonePage", params);
}

// 

/**
 * 获取可创建工单的流程列表
 * 获取可以操作信息
 */
export const getProcSelects = (p) => {
	return axios.get(`/flow/execute/operatorInfo?definitionId=${p.definitionId}&nowNodeCode=${p.nowNodeCode}`)
}

/**
 * 启动详情获取
 * 
 * @param {Object} procKey 流程标识
 */
export const startInfo = (procKey) => {
	return axios.post(`/eam-pm/app/${procKey}/start/info`, {})
}

/**
 * 待办详情获取
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} mainId  主单id
 * @param {Object} taskId  任务id
 */
export const todoDetailInfo = (procKey, mainId, taskId) => {
	return axios.get(`/eam-pm/app/todo/detail/${procKey}/${mainId}/${taskId}`)
}


/**
 * 流程记录
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} mainId  主单id
 */
export const historyAndsvg = (procKey, mainId) => {
	return axios.get(`/eam-pm/app/historyAndsvg/${procKey}/${mainId}`)
}

/**
 * 获取环节字段权限配置
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} mainId  主单id启动时为空
 * @param {Object} nodeId  节点id启动时为空
 */
export const settings = (procKey, mainId, nodeId) => {
	let query = "";
	if (mainId || nodeId) {
		query = "?mainId=" + (mainId || "") + "&nodeId=" + (nodeId || "");
	}
	return axios.get(`/eam-pm/app/${procKey}/settings` + query)
}

/**
 * 启动流程
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} params  启动流程body参数
 */
export const startProcess = (procKey, params) => {
	return axios.post(`/eam-pm/app/${procKey}/start`, {
		...(params || {})
	})
}

/**
 * 提交流程
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} mainId  主单id
 * @param {Object} taskId  任务id
 * @param {Object} params  启动流程body参数
 */
export const completeTask = (procKey, mainId, taskId, params) => {
	return axios.post(`/eam-pm/app/complete/${procKey}/${mainId}/${taskId}`, {
		...(params || {})
	})
}

/**
 * 受理任务
 * 
 * @param {Object} procKey 流程标识
 * @param {Object} mainId  主单id
 * @param {Object} taskId  任务id
 */
export const claimTask = (procKey, mainId, taskId) => {
	return axios.get(`/eam-pm/app/claimTask/${procKey}/${mainId}/${taskId}`)
}

/**
 * 查询部门
 */
export const queryDepts = (keywords) => {
	// /framework/sysmanage/dept/flatSearch
	// let query = "?limit=50&keyWord=" + (keywords);
	return axios.get(config.portalContextPathNew + "/framework/sysmanage/dept/tree")
}

/**
 * 查询全部部门（备用）
 */
export const getDeptInfos = (deptIds) => {
	// /framework/sysmanage/dept/flatSearch
	let query = "?deptIds=" + (deptIds || "");
	return axios.get(config.portalContextPathNew + "/framework/sysmanage/dept/getDeptInfos" + query)
}

/**
 * 获取部门下面的角色列表
 *
 * @param deptId
 */
export function loadParticipants(params) {
	return axios.get('/eam-pm/common/getRoleUsers', params)
}



// 获取可以操作信息
export function executeOperatorInfo(params) {
	return axios.get(`/flow/execute/operatorInfo?definitionId=${params.definitionId}&nowNodeCode=${params.nowNodeCode}`)
}


// 初始化技术变更单信息
export function workOrderInitWorkOrder(params) {
	return axios.get(`/workOrder/initWorkOrder?typeId=${params}`)
}

// 查询工单级别
export function workOrderListWorkOrderLevel(params) {
	return axios.get(`/workOrder/listWorkOrderLevel`)
}

// 提交技术变更工单
export const workOrderSubmit = (params) => {
	return axios.post(`/workOrder/submit`, {
		...(params || {})
	})
}

// 添加查询用户信息
export const sysmanageUsersList = (params) => {
	return axios.post(`${config.portalContextPathNew}/framework/sysmanage/users/list`, {
		...(params || {})
	})
}

// 根据 id 查询工单信息
export function workOrderGetById(id) {
	return axios.get(`/workOrder/getById?id=${id}`)
}

// 查询某条业务数据的处理记录
export function taskListDone(id) {
	return axios.get(`/flow/task/listDone?instanceId=${id}`)
}

// 待办处理
export const flowExecuteSkipFlow = (params) => {
	return axios.post(`/flow/execute/skipFlow`, {
		...(params || {})
	})
}


// 初始化通知单
export function noticeOrderInit() {
	return axios.get(`/notice/order/init`)
}

// 提交通知单
export const noticeOrderSave = (params) => {
	return axios.post(`/notice/order/save`, {
		...(params || {})
	})
}

// 通知单分页查询
export const noticeOrderListPage = (params) => {
	return axios.post(`/notice/order/listPage`, {
		...(params || {})
	})
}

// 根据 id 查询通知单
export function noticeOrderGetById(id) {
	return axios.get(`/notice/order/getById?id=${id}`)
}

// 获取实例流程图
export function flowExecuteGetWorkFlow(id) {
	return axios.get(`/flow/execute/getWorkFlow?instanceId=${id}`)
}

// 保存技术变更工单-流程扭转前先调用保存接口
export const workOrderSaveBeforeFlow = (params) => {
	return axios.post(`/workOrder/save`, {
		...(params || {})
	})
}
// 保存技术变更工单-流程扭转前先调用保存接口
export const flowExecuteCloseFlow = (params) => {
	return axios.post(`/flow/execute/closeFlow`, {
		...(params || {})
	})
}

// 上传附件
export const uploadFile = (data, businessId) => {
	return axios.post(`/attachment/upload/${businessId}`, data, {
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}


// 下载附件
export const downloadFile = (id) => {
	return axios.get(`/attachment/download/${id}`, {
		responseType: 'blob', // 必须声明响应类型为blob[2,7](@ref)
		headers: {
			'Cache-Control': 'no-cache', // 避免缓存问题
		}
	})
}

// 根据业务 id 查询附件信息
export const attachmentGetAttachmentByBusinessId = (businessId) => {
	return axios.get(`/attachment/getAttachmentByBusinessId/${businessId}`)
}

// 删除附件
export const attachmentDeleteAttachment = (businessId) => {
	return axios.delete(`/attachment/deleteAttachment/${businessId}`)
}