import axios from "/common/axios.js"
import config from "/common/config.js"


// 获取工单统计信息
export const workorderStatistics = (p) => {
	return axios.get(`/workorder/statistics/count?days=${p.days}`)
}

// 工单趋势图
export const workorderTrend = (p) => {
	return axios.get(`/workorder/statistics/trend?days=${p.days}`)
}

// 工单级别趋势图
export const workorderLevelTrend = (p) => {
	return axios.get(`/workorder/statistics/level/trend?days=${p.days}`)
}

// 工单级别汇总
export const workorderLevelSummary = (p) => {
	return axios.get(`/workorder/statistics/level/count?days=${p.days}`)
}