<template>
	<view class="deal-todo-list">
		<view class="main-form">
			<!-- 流程form -->
			<flow-form :key="formKey" ref="formForm" :model="formMain" :component-groups="componentGroups">
				<template #bottom>
					<uni-section style="padding: 0 16rpx;" title="工单详情" type="line">
						<uni-easyinput :disabled="isDraft != '拟稿'" v-model="valiFormData.detail" placeholder="工单描述信息"
							autoHeight :maxlength="1000" type="textarea"></uni-easyinput>

						<uni-forms-item class="uni-forms-item__content_file" label="附件">
							<view style="padding-bottom: 16rpx;color: #aaa;width: 100px;">可上传多个文件</view>
							<upload-demo :isDraft="isDraft" :businessId="this.valiFormData.id"
								type="file"></upload-demo>
						</uni-forms-item>
					</uni-section>

					<uni-section v-if="!nextNodeIsEnd" title="派发对象" type="line">
						<uni-group>
							<uni-forms-item label-width="130px" label="组织机构" name="deptName">
								<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
									:multiple='false' @select-change="selectChange" title="选择组织机构" :localdata="listData"
									valueKey="deptId" textKey="deptName" childrenKey="children" />
								<view @click="showPicker">
									<uni-easyinput :clearable="false" @focus="handleFocus"
										v-model="valiFormData.deptName" placeholder="请选择组织机构" />
								</view>

							</uni-forms-item>
							<uni-forms-item label="受理人">
								<pop-select v-if="participantOptionsView" @update:modelValue="userNamesDataUpdate"
									v-model="valiFormData.newuserNames" placeholder="请选择受理人" :multiple="true"
									:options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item>
							<!-- </template> -->

						</uni-group>
					</uni-section>

					<button style="float: right;margin: 16rpx;margin-top: 10rpx;" @click="viewFlowChart" type="primary"
						size="mini">查看流程图</button>
					<uni-section title="最新进度" type="line">
						<uni-group>
							<flow-steps class="process-steps" :options="processLinks" active-color="#007AFF"
								:active="processLinks.length - 1" direction="column">
								<template #item="{ item, index, isLastItem }">
									<!-- 第一个创建环节不显示 -->
									<view class="item">
										<view class="step-title" style="font-weight: bold;color: #10172A;">
											{{ item.nodeName }}</view>
										<view class="col">
											<view>处理人：</view>
											<view style="color: #3C5176;">{{ item.approverName }}</view>
										</view>
										<view class="col">
											<view>处理说明：</view>
											<view style="
												color: #3C5176;
												white-space: pre-wrap;  
												word-break: break-all;  
												width: 60vw;
												overflow: hidden;
												">
												{{ item.message || '-' }}
											</view>
										</view>
										<view class="col">
											<view>处理时间：</view>
											<view style="color: #3C5176;">{{ item.updateTime }}</view>
										</view>
									</view>
								</template>
							</flow-steps>
						</uni-group>
					</uni-section>
				</template>
			</flow-form>
		</view>
		<uni-row :gutter="20"
			style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
			<uni-col :span="canReject ? 12 : (valiFormData.nodeName == '拟稿' ? 12 : 24)">
				<button type="primary" @click="openDialog" :loading="submitLoading">
					{{ nextNodeIsEnd ? '归档' : '提交' }}
				</button>
			</uni-col>
			<!-- 添加撤销工单 -->
			<uni-col v-if="valiFormData.nodeName == '拟稿'"
				:span="canReject ? 12 : (valiFormData.nodeName == '拟稿' ? 12 : 0)">
				<button type="primary" @click="cancelOpenDialog">撤单</button>
			</uni-col>
			<uni-col v-if="canReject" :span="canReject ? 12 : 0">
				<button type="primary" @click="backOpenDialog">退回</button>
			</uni-col>
		</uni-row>


		<uni-popup ref="dialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">{{ dialogTitle }}</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="close">取消</button>
					<button type="primary" @click="confirm">确定</button>
				</view>
			</view>
		</uni-popup>
		<uni-popup ref="cancelOpenDialog" type="dialog">
			<view class="custom-dialog-new">
				<view class="title" style="padding-bottom: 23rpx;">提示</view>
				<view class="content">
					关闭表单后，所有填写内容将被永久删除且无法恢复，请确认是否继续？
				</view>
				<view class="footer" style="margin-top: 13px;">
					<button type="default" @click="cancelClose">取消</button>
					<button type="primary" @click="cancelConfirm">确认关闭</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="backDialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">工单退回</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="backClose">取消</button>
					<button type="primary" @click="backConfirm">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import FlowForm from "./flow-form.vue"
import FlowSteps from "./flow-steps.vue"
import list from "./list.js"
import flow from "./flow.js"
import {
	workOrderSaveBeforeFlow, getProcSelects, workOrderGetById,
	workOrderListWorkOrderLevel, queryDepts, sysmanageUsersList,
	taskListDone, flowExecuteSkipFlow, flowExecuteCloseFlow
} from "./api/index.js"
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue"
import UploadDemo from "/components/xe-upload_1/components/UploadDemo.vue"
import LFile from "/components/l-file_1-3/pages/index/index.vue"
export default {
	name: "deal-todo-list",
	components: {
		FlowForm, FlowSteps, baTreePicker, UploadDemo, LFile
	},
	mixins: [list, flow],
	data() {
		return {
			dialogTitle: "工单审批",
			dialogVisible: false,
			topActive: 0,
			list1: [
				{ desc: '周毛毛\n2018-11-11', title: '创建', },
				{ title: '告警处理', desc: '周毛毛\n2018-11-12' },
				{ title: '告警确认', desc: '周毛毛\n2018-11-13' },
				{ title: '归档', desc: '周毛毛\n2018-11-14' }
			],
			procKey: null,
			mainId: null,
			taskDefKey: null,
			taskId: null,

			// 表单key
			formKey: 1,
			// 表单模型对象
			formMain: {},
			// 流程模型定义信息
			processMain: {},
			// 组件分组
			componentGroups: [],

			// 受理人
			taskParticipant: {},
			// 抄送人
			taskParticipantcopy: {},
			// 选择分支
			nextActivity: {},
			// 基本信息
			baseParams: {},

			// 环节记录
			processLinks: [],
			active: 1,

			canReject: false,
			nextNodeIsEnd: false,
			valiFormData: {
				code: '',
				title: '',
				creatorName: '',
				orderLevel: "",
				detail: "",
				selectedData: [],
				deptName: "",
				deptId: "",
				userNames: "",
				userIds: "",
				easyDeac: "",
				newuserNames: ""
			},
			listData: [],
			participantOptions: [],
			participantOptionsView: true,

			businessId: '',
			businessType: "",

			// 确认当前状态是不是拟稿
			isDraft: "",

			levelListArray: []
		}
	},
	// 页面加载
	onLoad(page) {
		this.businessId = page.id;
		this.businessType = page.businessType;
		let record = this.getCurrent() || {};
		console.log("record", record);
		let { procKey, mainId, taskDefKey, taskId } = record;
		uni.setNavigationBarTitle({
			title: page.nodeName + " - 处理工单"
		});

		if (page.nodeName == '拟稿') {
			this.isDraft = '拟稿'
		} else {
			this.isDraft = '不是拟稿'
		}

		Object.assign(this, { procKey, mainId, taskDefKey, taskId });

		this.initFlow(page);
	},
	watch: {
		"valiFormData.deptId": {
			handler(newVal, oldVal) {
				console.log('deptId changed: ', newVal, oldVal);
				this.valiFormData.selectedData = [newVal];
				this.getSysmanageUsersList({
					"deptId": this.valiFormData.deptId,
					"pageNo": 1,
					"pageSize": 100000
				})
				this.participantOptionsView = false;
				this.$nextTick(() => {
					this.participantOptionsView = true;
					this.valiFormData.userIds = '';
					this.valiFormData.newuserNames = '';
					console.log("this.valiFormData.userIds", this.valiFormData.userIds)
				})
			},
			deep: true,
			important: true
		}
	},
	methods: {
		handleFocus() {
			uni.hideKeyboard();
			this.$nextTick(() => {
				uni.hideKeyboard(); // 隐藏键盘
				// 或通过DOM操作移开焦点
				// document.activeElement.blur();
			});
		},
		userNamesDataUpdate(userid, username) {
			console.log(userid, username);
			this.valiFormData.userNames = username;
			this.valiFormData.userIds = userid;
		},
		// 显示选择器
		showPicker() {
			this.$refs.treePicker._show();
		},
		getSysmanageUsersList(params) {
			sysmanageUsersList(params).then(res => {
				let list = [];
				if (res?.data?.length > 0) {
					res.data.forEach(item => {
						list.push({
							...item,
							label: item.realName,
							value: item.userId
						})
					});
				}
				console.log(list)
				this.participantOptions = list;
			})
		},
		//监听选择（ids为数组）
		selectChange(ids, names) {
			console.log(ids, names);
			this.valiFormData.selectedData = ids;
			this.valiFormData.deptName = names;
			this.valiFormData.deptId = ids[0];
		},
		viewFlowChart() {
			// viewFlowChart 去往viewFlowChart页
			uni.navigateTo({
				url: `/pages/list/viewFlowChart?instanceId=${this.valiFormData.instanceId}`
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			let pages = getCurrentPages(); // 当前页面
			let beforePage = pages[pages.length - 2]; // 前一个页面
			uni.navigateBack({
				success: function () {
					typeof (beforePage.refresh) == 'function' && beforePage.refresh();
				}
			});
		},
		initFlow(page) {
			queryDepts().then((res) => {
				this.listData = res.data;
				console.log(this.listData)
			})

			workOrderListWorkOrderLevel().then(res => {
				this.levelListArray = res.data;
				res.data.forEach(item => {
					item.text = item.label;
				})
				this.componentGroups = [
					{
						"appComponent": [
							{
								"field": "code",
								"name": "工单号",
								"uid": "code",
								"component": {
									"type": "Input",
									"attrs": {
										"readonly": 1,
										"hidden": 0,
										"required": 0
									},
									"props": {
										"placeholder": "请输入工单号"
									}
								}
							},
							{
								"field": "title",
								"name": "标题",
								"uid": "title",
								"component": {
									"type": "Input",
									"attrs": {
										"readonly": this.isDraft == '拟稿' ? 0 : 1,
										"hidden": 0,
										"required": 1
									},
									"props": {
										"placeholder": "请输入标题"
									}
								}
							},
							{
								"field": "creatorName",
								"name": "创建人",
								"uid": "creatorName",
								"component": {
									"type": "Input",
									"attrs": {
										"readonly": 1,
										"hidden": 0,
										"required": 0
									},
									"props": {
										"placeholder": "请输入创建人"
									}
								}
							},
							{
								"field": "deptName",
								"name": "创建人部门名称",
								"uid": "deptName",
								"component": {
									"type": "Input",
									"attrs": {
										"readonly": 1,
										"hidden": 0,
										"required": 0
									},
									"props": {
										"placeholder": "请输入创建人部门名称"
									}
								}
							},
							{
								"field": this.isDraft == '拟稿' ? "orderLevel" : "orderLevelName",
								"name": "工单级别",
								"uid": this.isDraft == '拟稿' ? "orderLevel" : "orderLevelName",
								"component": {
									"type": this.isDraft == '拟稿' ? "Select" : "input",
									"attrs": {
										"readonly": this.isDraft == '拟稿' ? 0 : 1,
										"hidden": 0,
										"required": 0
									},
									"props": {
										"placeholder": "请选择工单级别",

									},
									// 定义静态选项
									"options": res.data
								},
							},
						],
						"groupName": "基本信息"
					},
				]
			})



			// 获取可以操作信息
			getProcSelects({
				definitionId: page.definitionId,
				nowNodeCode: page.nowNodeCode,
			}).then(res => {
				this.canReject = res.data.canReject;
				this.nextNodeIsEnd = res.data.nextNodeIsEnd;
			})

			workOrderGetById(page.id).then(res => {
				console.log(res.data)
				const level = this.levelListArray;
				level.forEach(item => {
					if (item.value == res.data.orderLevel) {
						res.data.orderLevelName = item.label;
					}
				})
				this.formMain = res.data;
				this.valiFormData = {
					...res.data
				}

				taskListDone(res.data.instanceId).then(res => {
					let arr = res?.data?.reverse();
					this.processLinks = arr;
				})
			})
			this.getSysmanageUsersList({
				"deptId": "-1",
				"pageNo": 1,
				"pageSize": 100000
			});
		},
		openDialog() {
			const currentFormData = this.$refs?.formForm?.model;
			if (this.valiFormData?.deptName?.length == 0) {
				uni.showToast({
					title: '请选择受理人所属部门',
					duration: 2000,
					icon: 'none',
				});
				return false;
			}
			if (currentFormData.title == '' || !currentFormData.title) {
				uni.showToast({
					title: '请填写标题',
					icon: 'none'
				});
				return;
			}
			if (this.nextNodeIsEnd || (this.valiFormData.newuserNames && this.valiFormData.newuserNames.length > 0)) {
			} else {
				uni.showToast({
					title: '请选择受理人',
					icon: 'none'
				});
				return;
			}
			this.valiFormData.easyDeac = "";
			this.$refs.dialog.open();  // 动态打开对话框
		},
		close() {
			this.$refs.dialog.close(); // 关闭对话框
		},
		confirm() {
			// 处理确认逻辑
			this.completeTask('PASS');
		},
		cancelClose() {
			this.$refs.cancelOpenDialog.close(); // 关闭对话框
		},
		cancelConfirm() {
			flowExecuteCloseFlow({
				businessId: this.businessId,
				businessType: this.businessType,
				"skipType": "NONE"
			}).then(res => {
				if (res.status == 0) {
					uni.showToast({
						title: '流程关闭成功',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1000);
				} else {
					uni.showToast({
						title: '流程关闭失败',
						icon: 'none'
					});
				}

			})
		},
		cancelOpenDialog() {
			this.$refs.cancelOpenDialog.open();  // 动态打开对话框
		},
		backOpenDialog() {
			const currentFormData = this.$refs?.formForm?.model;
			if (currentFormData.title == '' || !currentFormData.title) {
				uni.showToast({
					title: '请填写标题',
					icon: 'none'
				});
				return;
			}
			if (this.valiFormData?.deptName?.length == 0) {
				uni.showToast({
					title: '请选择受理人所属部门',
					duration: 2000,
					icon: 'none',
				});
				return false;
			}
			if (this.valiFormData.newuserNames && this.valiFormData.newuserNames.length > 0) {
			} else {
				uni.showToast({
					title: '请选择受理人',
					icon: 'none'
				});
				return;
			}
			this.valiFormData.easyDeac = "";
			this.$refs.backDialog.open();  // 动态打开对话框
		},
		backClose() {
			this.$refs.backDialog.close(); // 关闭对话框
		},
		backConfirm() {
			// 处理确认逻辑
			this.completeTask('REJECT');
		},
		completeTask(skipType) {
			if (this.submitLoading) return;

			let formForm = this.$refs.formForm;
			formForm.validate().then(() => {
				uni.showLoading({
					title: "正在处理中...",
					icon: "loading"
				})
				this.submitLoading = true;
				const requestObject = {
					"skipType": skipType,
					"businessId": this.businessId,
					"businessType": this.businessType,
					"message": this.valiFormData.easyDeac,
					"userIds": this.valiFormData.newuserNames,
					"nextNodeIsEnd": this.nextNodeIsEnd
				};
				const postFormData = {
					...this.valiFormData,
					...this.formMain,
					detail: this.valiFormData.detail
				}

				this.levelListArray.forEach(item => {
					console.log(item);
					if (item.value == postFormData.orderLevel) {
						postFormData.orderLevelName = item.label;
						return;
					}
				})
				console.log(postFormData);
				// return;
				workOrderSaveBeforeFlow(postFormData).then(res => {
					// return;
					if (res.data) {
						flowExecuteSkipFlow(requestObject).then(res => {
							let resData = res.data;
							console.log("resData", resData);
							if (resData.length == '0') {
								uni.showToast({
									title: "操作成功"
								});
								this.backAndRefrush();
							} else {
								uni.showToast({
									title: res.data,
									icon: "none"
								});
								console.error("res", res);
							}
						}).catch(err => {
							console.error(err);
							uni.showToast({
								title: "操作失败"
							})
						}).then(() => {
							this.submitLoading = false;
						});
					} else {
						uni.showToast({
							title: "操作失败"
						})
						this.submitLoading = false;
					}

				}).catch(err => {
					console.error(err);
					uni.showToast({
						title: "操作失败"
					})
				}).then(() => {
					this.submitLoading = false;
				});

			}).catch(data => {
				console.log(data);
				let msg = "校验未通过";
				if (Array.isArray(data)) {
					let firstField = data[0].key;
					formForm.scrollPropertyToView(firstField);
				}
				uni.showToast({
					title: msg,
					duration: 1000
				})
			});
		}
	}
}
</script>
<style lang="scss" scoped>
.uni-forms-item__content_file {
	:deep(.uni-forms-item__content) {
		width: 66px;
	}
}
</style>
<style lang="scss" scoped>
.deal-todo-list {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;


		.process-steps {
			.item {
				.col {
					display: flex;
					margin: 10px 0;
				}
			}
		}
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}
}

.custom-dialog {
	width: 79vw;
	background: #fff;
	border-radius: 66rpx;
	padding: 30rpx;

	.uni-textarea {
		width: auto;
		margin-top: 26rpx !important;
		border-radius: 16rpx;
	}
}

.custom-dialog-new {
	width: 79vw;
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	padding-top: 43rpx;
	font-size: 16px;
	color: #333;

}

.title {
	width: 100%;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.footer {
	display: flex;
	justify-content: center;

	button {
		font-size: 26rpx;
		margin: 20rpx;
		padding: 0rpx 56rpx;
	}
}
</style>