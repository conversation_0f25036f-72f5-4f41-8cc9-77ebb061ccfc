<template>
    <view class="draft-list">
        <view class="main-form">
            <!-- flow form -->
            <flow-form ref="formForm" :key="formKey" :model="formMain" :component-groups="componentGroups">
                <template #bottom>
                    <uni-section title="基本信息" type="line">
                        <view class="example" style="padding: 26rpx;padding-bottom: 16rpx;">
                            <!-- 基础表单校验 -->
                            <uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="工单编号" name="name">
                                    <uni-easyinput v-model="valiFormData.name" placeholder="请输入工单编号" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="标题" required name="age">
                                    <uni-easyinput v-model="valiFormData.age" placeholder="请输入标题" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="创建人" name="introduction">
                                    <uni-easyinput v-model="valiFormData.introduction" placeholder="请输入创建人" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="创建部门" name="introduction">
                                    <uni-easyinput v-model="valiFormData.introduction" placeholder="请输入创建部门" />
                                </uni-forms-item>
                                <uni-forms-item label-width="130px" label="通知级别" name="introduction">
                                    <pop-select v-model="taskParticipant.taskParticipant" placeholder="请选择通知级别"
                                        :multiple="grabSingle" :options="noticeLevelArr" value-to-string />
                                    <!-- <uni-easyinput v-model="valiFormData.introduction" placeholder="请输入通知级别" /> -->
                                </uni-forms-item>
                            </uni-forms>
                        </view>
                    </uni-section>
                    <uni-section style="margin-top: -40rpx;" title="告警详情" type="line">
                        <uni-group>
                            <view style="
							width: 100%;
							min-height: 100px;
							background-color: #eee;
							border: 2rpx solid #ccc;
							padding: 16rpx 16rpx 16rpx 16rpx;
							box-sizing: border-box;
							border-radius: 16rpx;">
                                <uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
                                    :width="nvueWidth">
                                    <uni-col :span="6">
                                        告警标题
                                    </uni-col>
                                    <uni-col style="font-size: 23rpx;color: #666;" :span="18">
                                        xxxxxx
                                    </uni-col>
                                </uni-row>
                                <uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
                                    :width="nvueWidth">
                                    <uni-col :span="6">
                                        告警对象
                                    </uni-col>
                                    <uni-col style="font-size: 23rpx;color: #666;" :span="18">
                                        192.0.0.1
                                    </uni-col>
                                </uni-row>
                                <uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
                                    :width="nvueWidth">
                                    <uni-col :span="6">
                                        告警正文
                                    </uni-col>
                                    <uni-col style="font-size: 23rpx;color: #666;" :span="18">
                                        CPU使用率超阈值，当前值为90%
                                    </uni-col>
                                </uni-row>
                                <uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
                                    :width="nvueWidth">
                                    <uni-col :span="6">
                                        告警发生时间
                                    </uni-col>
                                    <uni-col style="font-size: 23rpx;color: #666;" :span="18">
                                        2024-12-03 00:12:00
                                    </uni-col>
                                </uni-row>
                            </view>
                        </uni-group>
                    </uni-section>
                    <uni-section title="派发对象" type="line">
                        <uni-group>
                            <uni-forms-item label="组织机构">
                                <pop-select v-model="taskParticipant.dept" placeholder="请选择组织机构" remote
                                    :request="queryDepts">
                                    <template #label="option">
                                        <view>{{ option.deptPathName }}</view>
                                    </template>
                                </pop-select>
                            </uni-forms-item>
                            <uni-forms-item label="受理人">
                                <pop-select v-model="taskParticipant.taskParticipant" placeholder="请选择受理人"
                                    :multiple="grabSingle" :options="participantOptions" value-to-string />
                            </uni-forms-item>
                        </uni-group>
                    </uni-section>
                    <uni-section title="预约派发通知单" type="line">
                        <template v-slot:right>
                            <switch checked @change="switchNoticeHandle" style="transform:scale(0.8);" />
                        </template>
                        <uni-group v-if="switchNotice">
                            <uni-forms-item label="选择派发时间">
                                <uni-datetime-picker type="datetime" v-model="datetimesingle" @change="changeLog" />
                            </uni-forms-item>
                        </uni-group>
                    </uni-section>
                </template>
            </flow-form>
        </view>
        <uni-row :gutter="20"
            style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
            <uni-col :span="12">
                <button type="primary" @click="startProcess" :loading="submitLoading">派发</button>
            </uni-col>
            <uni-col :span="12">
                <button type="primary" @click="back">取消</button>
            </uni-col>
        </uni-row>
    </view>


</template>

<script>
import FlowForm from "./flow-form.vue"
import list from "./list.js"
import flow from "./flow.js"
import { startInfo, settings, startProcess } from "./api/index.js"
export default {
    name: "draft-list",
    components: {
        FlowForm
    },
    mixins: [list, flow],
    data() {
        return {
            noticeLevelArr: [
                { "value": "urgent", "label": "紧急" },
                { "value": "normal", "label": "普通" }
            ],
            datetimesingle: '',
            switchNotice: true,
            procKey: null,
            procName: null,

            // 表单key
            formKey: 1,
            // 表单模型对象
            formMain: {},
            // 流程模型定义信息
            processMain: {},
            // 组件分组
            componentGroups: [],

            // 基本信息
            baseParams: {},

            operateState: "todo",

            submitLoading: false,

            pageTitle: '',

            // 校验表单数据
            valiFormData: {
                name: '',
                age: '',
                introduction: '',
            },
            // 校验规则
            rules: {
                age: {
                    rules: [{
                        required: true,
                        errorMessage: '标题不能为空'
                    }]
                }
            },
        }
    },

    // 页面加载
    onLoad(page) {

    },

    methods: {
        changeLog(val) { },
        switchNoticeHandle(val) {
            this.switchNotice = !this.switchNotice;
        },
        back() {
            uni.navigateBack({
                delta: 1
            })
        },
        backAndRefrush() {
            let pages = getCurrentPages(); // 当前页面
            let beforePage = pages[pages.length - 2]; // 前一个页面
            uni.navigateBack({
                success: function () {
                    typeof (beforePage.refresh) == 'function' && beforePage.refresh();
                }
            });
        },
        initFlow() {
        },
        startProcess() {
            if (this.submitLoading) return;

            let formForm = this.$refs.formForm;
            this.$refs['valiForm'].validate().then(res => {
                uni.showLoading({
                    title: "提交工单...",
                    icon: "loading"
                })
                console.log('success', res);
                uni.showToast({
                    title: `校验通过`
                })
            }).catch(err => {
                console.log('err', err);
                if (Array.isArray(data)) {
                    let firstField = data[0].key;
                    this.$refs['valiForm'].scrollPropertyToView(firstField);
                }
                uni.showToast({
                    title: msg,
                    duration: 1000
                })
            })
        }
    },
    watch: {
    }
}
</script>

<style lang="scss" scoped>
.draft-list {
    .main-form {
        /* #ifndef H5 */
        height: calc(100vh - 60px);
        /* #endif */
        /* #ifdef H5 */
        height: calc(100vh - 100px);
        /* #endif */
        overflow: auto;
    }
}
</style>