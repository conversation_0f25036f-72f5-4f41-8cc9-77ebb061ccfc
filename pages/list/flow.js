import {queryDepts, loadParticipants} from "./api/index.js"
export default {
	data() {
		return {
		  currentOperate: null,
		  nextOperates: [],
			
		  // 选择分支
		  nextActivity: {},
		  nextActivities: [],
			
		  globalButtons: [],
		  buttons: [],
		  
		  // 参与人待选列表
		  participantOptions: [],
		  // 受理人
		  taskParticipant: {},
		  // 抄送人
		  taskParticipantcopy: {},
		}
	},
	computed: {
		isTodo() {
		  return this.operateState == "todo";
		},
		isDone() {
		  return this.operateState == "done";
		},
		// 是否归档结束
		isEnd() {
		  let { activityType } = this.nextActivity;
		  return activityType == "EndEvent";
		},
		/**是否需要派发*/
		needReceiver() {
		  let propertyMap = this.nextActivity.camundaPropertyMap || {};
		  return !this.isEnd && propertyMap.needReceiver !== "false";
		},
		/*是否需要抄送*/
		needCopy() {
		  let propertyMap = this.nextActivity.camundaPropertyMap || {};
		  return !this.isEnd && propertyMap.needCopy == "true";
		},
		// 是否需要抢单(控制多选/单选)
		grabSingle() {
		  let processLink = this.nextActivity.processLink || {};
		  return !!processLink.grabSingle;
		},
		// 角色id
		roleId() {
		  let processLink = this.nextActivity.processLink || {};
		  return processLink.roleId;
		},
		// 是否显示派发组件
		roleShow() {
		  let processLink = this.nextActivity.processLink || {};
		  return processLink.roleShow != false;
		},
		// 派发类型：0个人/1组织/2角色
		sendAll() {
		  let processLink = this.nextActivity.processLink || {};
		  return processLink.sendAll || 0;
		},
	
		// 是否显示组织选人控件,当且仅当sendAll=2 && roleShow = fasle时不显示，其他情况都显示
		showDeptParticipant() {
		  return this.sendAll != 2 || this.roleShow;
		},
		
		// 是否需要受理（是）且同时未受理过
		claimable() {
		  // return this.checkButtonAble("claim");
		  return this.needClaim && !this.isClaimed;
		},
		delegateable() {
		  return this.checkButtonAble("delegate");
		},
		revokeable() {
		  return this.checkButtonAble("revoke");
		},
		stopable() {
		  return this.checkButtonAble("stop");
		},
		saveable() {
		  return this.checkButtonAble("save");
		},
		submitProgressAble() {
		  return this.checkButtonAble("submitProgress");
		}
	},
	methods: {
		checkButtonAble(type) {
		  let { globalButtons, buttons } = this;
		  if (buttons && buttons.length > 0) {
			return buttons.includes(type);
		  }
		  return globalButtons && globalButtons.includes(type);
		},
		/** 查询部门信息 */
		queryDepts(queryValue) {
			return new Promise(resolve => {
				// resolve({
				// 	data: [
				// 		{label: "测试部门", value: "65STz17n59qiacrV2GO"},
				// 	]
				// }); 
				queryDepts(queryValue).then(res => {
					let resData = res?.data?.map((deptInfo) => {
						let {deptId, deptName, deptPathName} = deptInfo;
						return {
							label: deptName,
							value: deptId,
							deptPathName
						}
					});
					resolve({
						data: resData
					});
				}).catch(err => {
					console.error(err);
					resolve({data: []});
				});
				
			});
		},
		/** 查询参与者列表 */
		queryTaskParticipants() {
			let deptId = this.taskParticipant.dept;
			if(!deptId) {
				return;
			}
		    const queryParams = {
				...(this.baseParams || {}),
				deptId,
			    procKey: this.procKey,
		    };
			if (this.roleId) {
				queryParams.roleCodes = this.roleId;
			}
		    this.loading = true;
			
			this.participantOptions.splice(0, this.participantOptions.length);
		    loadParticipants(queryParams)
			.then(response => {
			   let options = response.data.treeData
				.filter(item => !!item)[0]
				.children.filter(item => !!item).map(item => {
					return {
						value: item.id,
						label: item.label
					}
				});
			   this.participantOptions.push(...options);
			   
			   let selectVal = this.taskParticipant.taskParticipant;
			   if(this.taskParticipant.taskParticipant) {
				   let selectParticipant = this.participantOptions.find(participantOption => participantOption.value == selectVal);
				   if(!selectParticipant) {
					   // 情况选择
					   this.taskParticipant.taskParticipant = "";
				   }
			   }
			   
			   
			})
			.catch(reason => {
			  console.info(reason);
			}).then(() => (this.loading = false));
		},
	},
	watch: {
		currentOperate(val) {
			if(val) {
				let nextActivity = this.nextActivities.find(nextActivity => nextActivity.operateDefKey == val);
				this.nextActivity = nextActivity;
			}
		},
		"taskParticipant.dept"(val) {
			// 加载人员
			this.queryTaskParticipants();
		}
	}
}