<template>
	<view class="deal-todo-list" :class="{ 'deal-todo-list-dark': theme }">
		<view class="main-form">
			<!-- <view>
				<uni-section title="" type="" padding>
					<uni-steps class="uni-steps-Custom" :options="list1" :active="topActive">
					</uni-steps>
				</uni-section>
			</view> -->

			<!-- 流程form -->
			<flow-form :key="formKey" ref="formForm" :model="formMain" :component-groups="componentGroups">
				<template #bottom>
					<uni-section style="padding: 0 16rpx;" title="工单详情" type="line">
						<uni-easyinput disabled v-model="valiFormData.detail" placeholder="工单描述信息" autoHeight
							:maxlength="1000" type="textarea"></uni-easyinput>
						<!-- <view style="
							width: 100%;
							min-height: 100px;
							background-color: #eee;
							border: 2rpx solid #ccc;
							padding: 16rpx 16rpx 16rpx 16rpx;
							box-sizing: border-box;
							border-radius: 16rpx;">
							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col :span="6">
									告警标题
								</uni-col>
								<uni-col style="font-size: 23rpx;color: #666;" :span="18">
									xxxxxx
								</uni-col>
							</uni-row>
							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col :span="6">
									告警对象
								</uni-col>
								<uni-col style="font-size: 23rpx;color: #666;" :span="18">
									192.0.0.1
								</uni-col>
							</uni-row>
							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col :span="6">
									告警正文
								</uni-col>
								<uni-col style="font-size: 23rpx;color: #666;" :span="18">
									CPU使用率超阈值，当前值为90%
								</uni-col>
							</uni-row>
							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col :span="6">
									告警发生时间
								</uni-col>
								<uni-col style="font-size: 23rpx;color: #666;" :span="18">
									2024-12-03 00:12:00
								</uni-col>
							</uni-row>
							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col :span="6">
									解决方案
								</uni-col>
								<uni-col style="font-size: 23rpx;color: #666;" :span="18">
									查看任务管理器，按 Ctrl+Shift+Esc 打开任务管理器，切换到「性能」选项卡，确认 CPU 使用率是否持续高于阈值。
									切换到「进程」选项卡，按 CPU 使用率排序，找出异常占用的进程（如某个应用、后台服务或病毒程序）。
									处理方法：右键结束高占用进程（谨慎结束系统关键进程，如 System Idle Process 是正常现象）。
									关闭高负荷应用
									禁用不必要的后台应用：进入「设置」>「应用」>「后台应用」，关闭非必要程序。
									暂停系统更新 / 备份：检查 Windows 更新状态，或暂停 OneDrive、iCloud 等同步任务。
								</uni-col>
							</uni-row>
						</view> -->
						<uni-forms-item class="uni-forms-item__content_file" label="附件">
							<view style="padding-bottom: 16rpx;color: #aaa;width: 100px;"></view>
							<upload-demo :isDraft="'no拟稿'" :businessId="this.valiFormData.id"
								type="file"></upload-demo>
						</uni-forms-item>
					</uni-section>

					<!-- <uni-section title="派发对象" type="line">
						<uni-group>
							<uni-forms-item label-width="130px" label="组织机构" name="deptName">
								<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
									:multiple='false' @select-change="selectChange" title="选择组织机构" :localdata="listData"
									valueKey="deptId" textKey="deptName" childrenKey="children" />
								<view @click="showPicker">
									<uni-easyinput v-model="valiFormData.deptName" placeholder="请选择组织机构" />
								</view>

							</uni-forms-item>
							<uni-forms-item label="受理人">
								<pop-select v-if="participantOptionsView" @update:modelValue="userNamesDataUpdate"
									v-model="valiFormData.newuserNames" placeholder="请选择受理人" :multiple="true"
									:options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
</pop-select>
</uni-forms-item>
</uni-group>
</uni-section> -->

					<button style="float: right;margin: 16rpx;margin-top: 10rpx;" @click="viewFlowChart" type="primary"
						size="mini">查看流程图</button>
					<uni-section title="最新进度" type="line">
						<uni-group>
							<flow-steps class="process-steps" :options="processLinks" active-color="#007AFF"
								:active="processLinks.length - 1" direction="column">
								<template #item="{ item, index, isLastItem }">
									<!-- 第一个创建环节不显示 -->
									<view class="item">
										<view class="step-title" style="font-weight: bold;color: #10172A;">
											{{ item.nodeName }}</view>
										<view class="col">
											<view>派单人：</view>
											<view style="color: #3C5176;">{{ item.approverName }}</view>
										</view>
										<view v-if="item.message != '_'&&item.message" class="col">
											<view>处理说明：</view>
											<view style="
												color: #3C5176;
												white-space: pre-wrap;
												word-break: break-all;
												width: 60vw;
												overflow: hidden;
												">
												{{ item.message || '-' }}
											</view>
										</view>
										<!-- <view class="col">
											<view>所属部门：</view>
											<view style="color: #3C5176;">{{ item.createByDeptname }}</view>
										</view> -->
										<view class="col">
											<view>派单时间：</view>
											<view style="color: #3C5176;">{{ item.updateTime }}</view>
										</view>
									</view>
								</template>
							</flow-steps>
						</uni-group>
					</uni-section>

					<view style="height: 6vh;" :style="{ backgroundColor: theme ? '#2b2b2b' : '#fff' }"></view>
				</template>
			</flow-form>
		</view>
		<uni-row :gutter="20"
			:style="{ backgroundColor: theme ? '#2b2b2b' : '#fff', margin: '0 auto', position: 'absolute', bottom: '10rpx', width: '100%', padding: '10rpx', boxSizing: 'border-box' }">
			<!-- <uni-col :span="canReject ? 12 : 24">
				<button type="primary" @click="openDialog" :loading="submitLoading">提交</button>
			</uni-col>
			<uni-col v-if="canReject" :span="canReject ? 12 : 0">
				<button type="primary" @click="backOpenDialog">退回</button>
			</uni-col> -->
			<uni-col :span="24">
				<button type="primary" @click="back">返回</button>
			</uni-col>
		</uni-row>


		<uni-popup ref="dialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">{{ dialogTitle }}</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="close">取消</button>
					<button type="primary" @click="confirm">确定</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="backDialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">工单退回</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="backClose">取消</button>
					<button type="primary" @click="backConfirm">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>


</template>

<script>
import FlowForm from "./flow-form.vue"
import FlowSteps from "./flow-steps.vue"
import list from "./list.js"
import flow from "./flow.js"
import {
	completeTask, getProcSelects, workOrderGetById,
	workOrderInitWorkOrder, workOrderListWorkOrderLevel,
	workOrderSubmit, queryDepts, sysmanageUsersList,
	taskListDone, flowExecuteSkipFlow
} from "./api/index.js"
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue"
import UploadDemo from "/components/xe-upload_1/components/UploadDemo.vue"
export default {
	name: "deal-todo-list",
	components: {
		FlowForm, FlowSteps, baTreePicker,UploadDemo
	},
	mixins: [list, flow],
	data() {
		return {
			dialogTitle: "处理说明",
			dialogVisible: false,
			topActive: 0,
			list1: [
				{ desc: '周毛毛\n2018-11-11', title: '创建', },
				{ title: '告警处理', desc: '周毛毛\n2018-11-12' },
				{ title: '告警确认', desc: '周毛毛\n2018-11-13' },
				{ title: '归档', desc: '周毛毛\n2018-11-14' }
			],
			procKey: null,
			mainId: null,
			taskDefKey: null,
			taskId: null,

			// 表单key
			formKey: 1,
			// 表单模型对象
			formMain: {},
			// 流程模型定义信息
			processMain: {},
			// 组件分组
			componentGroups: [],

			// 受理人
			taskParticipant: {},
			// 抄送人
			taskParticipantcopy: {},
			// 选择分支
			nextActivity: {},
			// 基本信息
			baseParams: {},

			// 环节记录
			processLinks: [],
			active: 1,

			canReject: false,
			nextNodeIsEnd: false,
			valiFormData: {
				code: '',
				title: '',
				creatorName: '',
				orderLevel: "",
				detail: "",
				selectedData: [],
				deptName: "",
				deptId: "",
				userNames: "",
				userIds: "",
				easyDeac: "",
				newuserNames: ""
			},
			listData: [],
			participantOptions: [],
			participantOptionsView: true,

			businessId: '',
			businessType: "",
			theme: false
		}
	},
	// 页面加载
	onLoad(page) {
		this.businessId = page.id;
		this.businessType = page.businessType;
		let record = this.getCurrent() || {};
		console.log("record", record);
		let { procKey, mainId, taskDefKey, taskId } = record;
		// uni.setNavigationBarTitle({
		// 	title: page.nodeName + " - 处理工单"
		// });
		uni.setNavigationBarTitle({
			title: '工单详情'
		});

		Object.assign(this, { procKey, mainId, taskDefKey, taskId });

		this.initFlow(page);

		// 初始化主题
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onShow() {
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	watch: {
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		},
		"valiFormData.deptId": {
			handler(newVal, oldVal) {
				console.log('deptId changed: ', newVal, oldVal);
				this.valiFormData.selectedData = [newVal];
				this.getSysmanageUsersList({
					"deptId": this.valiFormData.deptId,
					"pageNo": 1,
					"pageSize": 100000
				})
				this.participantOptionsView = false;
				setTimeout(() => {
					this.participantOptionsView = true;
				}, 333);
			},
			deep: true,
			important: true
		}
	},
	methods: {
		userNamesDataUpdate(userid, username) {
			console.log(userid, username);
			this.valiFormData.userNames = username?.slice(2);
			this.valiFormData.userIds = userid;
		},
		// 显示选择器
		showPicker() {
			this.$refs.treePicker._show();
		},
		getSysmanageUsersList(params) {
			sysmanageUsersList(params).then(res => {
				let list = [];
				if (res?.data?.length > 0) {
					res.data.forEach(item => {
						list.push({
							...item,
							label: item.realName,
							value: item.userId
						})
					});
				}
				console.log(list)
				this.participantOptions = list;
			})
		},
		//监听选择（ids为数组）
		selectChange(ids, names) {
			console.log(ids, names);
			this.valiFormData.selectedData = ids;
			this.valiFormData.deptName = names;
			this.valiFormData.deptId = ids[0];
		},
		viewFlowChart() {
			// viewFlowChart 去往viewFlowChart页
			uni.navigateTo({
				url: `/pages/list/viewFlowChart?instanceId=${this.valiFormData.instanceId}`
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			let pages = getCurrentPages(); // 当前页面
			let beforePage = pages[pages.length - 2]; // 前一个页面
			uni.navigateBack({
				success: function () {
					typeof (beforePage.refresh) == 'function' && beforePage.refresh();
				}
			});
		},
		initFlow(page) {
			queryDepts().then((res) => {
				this.listData = res.data;
				console.log(this.listData)
			})

			this.componentGroups = [
				{
					"appComponent": [
						{
							"field": "code",
							"name": "工单号",
							"uid": "code",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入工单号"
								}
							}
						},
						{
							"field": "title",
							"name": "标题",
							"uid": "title",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 1
								},
								"props": {
									"placeholder": "请输入标题"
								}
							}
						},
						{
							"field": "creatorName",
							"name": "创建人",
							"uid": "creatorName",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入创建人"
								}
							}
						},
						{
							"field": "deptName",
							"name": "创建人部门名称",
							"uid": "deptName",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入创建人部门名称"
								}
							}
						},
						{
							"field": "orderLevelName",
							"name": "工单级别",
							"uid": "orderLevelName",
							"component": {
								"type": "input",
								// 可选值为 紧急、重要、一般

								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择工单级别",

								},
								// 定义静态选项
								"options": [
									{ "value": "EMER", "label": "紧急" },
									{ "value": "MAJOR", "label": "重要" },
									{ "value": "GENERIC", "label": "一般" }
								]
							},
						},
					],
					"groupName": "基本信息"
				},
			]

			// 获取可以操作信息
			getProcSelects({
				definitionId: page.definitionId,
				nowNodeCode: page.nowNodeCode,
			}).then(res => {
				this.canReject = res.data.canReject;
				this.nextNodeIsEnd = res.data.nextNodeIsEnd;
			})

			workOrderGetById(page.id).then(res => {
				console.log(res.data)
				const level = [
					{ "value": "EMER", "label": "紧急" },
					{ "value": "MAJOR", "label": "重要" },
					{ "value": "GENERIC", "label": "一般" }
				]
				level.forEach(item => {
					if (item.value == res.data.orderLevel) {
						res.data.orderLevelName = item.label;
					}
				})
				this.formMain = res.data;
				this.valiFormData = {
					...res.data
				}
				// if (res.data.nodeName == '拟稿') {
				// 	this.componentGroups = [
				// 		{
				// 			"appComponent": [
				// 				{
				// 					"field": "code",
				// 					"name": "工单号",
				// 					"uid": "code",
				// 					"component": {
				// 						"type": "Input",
				// 						"attrs": {
				// 							"readonly": 1,
				// 							"hidden": 0,
				// 							"required": 0
				// 						},
				// 						"props": {
				// 							"placeholder": "请输入工单号"
				// 						}
				// 					}
				// 				},
				// 				{
				// 					"field": "title",
				// 					"name": "标题",
				// 					"uid": "title",
				// 					"component": {
				// 						"type": "Input",
				// 						"attrs": {
				// 							"readonly": 0,
				// 							"hidden": 0,
				// 							"required": 1
				// 						},
				// 						"props": {
				// 							"placeholder": "请输入标题"
				// 						}
				// 					}
				// 				},
				// 				{
				// 					"field": "creatorName",
				// 					"name": "创建人",
				// 					"uid": "creatorName",
				// 					"component": {
				// 						"type": "Input",
				// 						"attrs": {
				// 							"readonly": 1,
				// 							"hidden": 0,
				// 							"required": 0
				// 						},
				// 						"props": {
				// 							"placeholder": "请输入创建人"
				// 						}
				// 					}
				// 				},
				// 				{
				// 					"field": "deptName",
				// 					"name": "创建人部门名称",
				// 					"uid": "deptName",
				// 					"component": {
				// 						"type": "Input",
				// 						"attrs": {
				// 							"readonly": 1,
				// 							"hidden": 0,
				// 							"required": 0
				// 						},
				// 						"props": {
				// 							"placeholder": "请输入创建人部门名称"
				// 						}
				// 					}
				// 				},
				// 				{
				// 					"field": "orderLevelName",
				// 					"name": "工单级别",
				// 					"uid": "orderLevelName",
				// 					"component": {
				// 						"type": "input",
				// 						// 可选值为 紧急、重要、一般

				// 						"attrs": {
				// 							"readonly": 1,
				// 							"hidden": 0,
				// 							"required": 0
				// 						},
				// 						"props": {
				// 							"placeholder": "请选择工单级别",

				// 						},
				// 						// 定义静态选项
				// 						"options": [
				// 							{ "value": "EMER", "label": "紧急" },
				// 							{ "value": "MAJOR", "label": "重要" },
				// 							{ "value": "GENERIC", "label": "一般" }
				// 						]
				// 					},
				// 				},
				// 			],
				// 			"groupName": "基本信息"
				// 		},
				// 	]
				// }
				taskListDone(res.data.instanceId).then(res => {
					let arr = res?.data?.reverse();
					this.processLinks = arr;
				})
			})
			this.getSysmanageUsersList({
				"deptId": "-1",
				"pageNo": 1,
				"pageSize": 100000
			});
		},
		openDialog() {
			this.valiFormData.easyDeac = "";
			this.$refs.dialog.open();  // 动态打开对话框
		},
		close() {
			this.$refs.dialog.close(); // 关闭对话框
		},
		confirm() {
			// 处理确认逻辑
			this.completeTask('PASS');
		},
		backOpenDialog() {
			this.valiFormData.easyDeac = "";
			this.$refs.backDialog.open();  // 动态打开对话框
		},
		backClose() {
			this.$refs.backDialog.close(); // 关闭对话框
		},
		backConfirm() {
			// 处理确认逻辑
			this.completeTask('REJECT');
		},
		completeTask(skipType) {
			if (this.submitLoading) return;

			let formForm = this.$refs.formForm;
			formForm.validate().then(() => {
				uni.showLoading({
					title: "正在处理中...",
					icon: "loading"
				})
				this.submitLoading = true;
				const requestObject = {
					"skipType": skipType,
					"businessId": this.businessId,
					"businessType": this.businessType,
					"message": this.valiFormData.easyDeac,
					"userIds": this.valiFormData.newuserNames,
					"nextNodeIsEnd": this.nextNodeIsEnd
				};
				console.log(requestObject);
				// return;
				flowExecuteSkipFlow(requestObject).then(res => {
					let resData = res.data;
					console.log("resData", resData);
					if (resData.length == '0') {
						uni.showToast({
							title: "操作成功"
						});
						this.backAndRefrush();
					} else {
						uni.showToast({
							title: res.data
						});
						console.error("res", res);
					}
				}).catch(err => {
					console.error(err);
					uni.showToast({
						title: "操作失败"
					})
				}).then(() => {
					this.submitLoading = false;
				});
			}).catch(data => {
				console.log(data);
				let msg = "校验未通过";
				if (Array.isArray(data)) {
					let firstField = data[0].key;
					formForm.scrollPropertyToView(firstField);
				}
				uni.showToast({
					title: msg,
					duration: 1000
				})
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.deal-todo-list {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;


		.process-steps {
			.item {
				.col {
					display: flex;
					margin: 10px 0;
				}
			}
		}
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}
}

.custom-dialog {
	width: 79vw;
	background: #fff;
	border-radius: 66rpx;
	padding: 30rpx;

	.uni-textarea {
		width: auto;
		margin-top: 26rpx !important;
		border-radius: 16rpx;
	}
}

.title {
	width: 100%;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.footer {
	display: flex;
	justify-content: center;

	button {
		font-size: 26rpx;
		margin: 20rpx;
		padding: 0rpx 56rpx;
	}
}

.deal-todo-list-dark {
	background: #2b2b2b;

	:deep(.uni-section) {
		background: #2b2b2b;

		span {
			color: #fff;
		}
	}

	:deep(.uni-group__content) {
		background: #2b2b2b;
	}

	:deep(.uni-forms-item--border) {
		border-top: 1px #aaa6a6 solid;
	}

	:deep(.uni-forms-item__content) {
		color: #ffffffd2;
	}

	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;

		.process-steps {
			.item {
				.col {
					display: flex;
					margin: 10px 0;
				}
			}
		}
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}

	.custom-dialog {
		width: 79vw;
		background: #2b2b2b !important;
		color: #fff !important;
		border-radius: 66rpx;
		padding: 30rpx;

		.uni-textarea {
			width: auto;
			margin-top: 26rpx !important;
			border-radius: 16rpx;
		}
	}

	.title {
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
	}

	.footer {
		display: flex;
		justify-content: center;

		button {
			font-size: 26rpx;
			margin: 20rpx;
			padding: 0rpx 56rpx;
		}
	}

	:deep(.uni-popup) {
		.uni-popup__wrapper {
			background: #2b2b2b !important;
		}
	}

	:deep(input) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-easyinput__content) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.flow-steps__column-text) {
		color: #fff !important;

		.step-title {
			color: #fff !important;
		}

		.col uni-view {
			color: #ffffffc3 !important;
		}
	}

	:deep(.uni-section__content-title) {
		color: #fff !important;
	}

	:deep(.uni-forms-item__label) {
		color: #fff !important;
	}
}
</style>