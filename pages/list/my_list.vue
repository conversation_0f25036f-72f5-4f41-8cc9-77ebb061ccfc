<template>
	<view class="my_list" :class="{ 'my_list-dark': theme }" style="overflow: visible !important;">
		<!-- 工单搜索框 -->
		<view class="search-bar">
			<uni-search-bar @clear="clearSearch" class="list-searchbar" style="flex: 1;padding-right: 0;" always
				placeholder="工单标题" cancel-button="none" @confirm="search" v-model="searchValue.title">
			</uni-search-bar>
			<uni-search-bar @clear="clearSearch" class="list-searchbar" style="flex: 1;padding-left: 6px;" always
				placeholder="工单编号" cancel-button="none" @confirm="search" v-model="searchValue.code">
			</uni-search-bar>
			<DaDropdown class="search-bar-dropdown" style="width: 53px;" v-model:dropdownMenu="dropdownMenuList"
				fixedTop :fixedTopValue="10" @confirm="handleConfirm" @close="handleClose" @open="handleOpen">
			</DaDropdown>
		</view>
		<!-- tab页 -->
		<scroll-view ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
			:scroll-left="scrollLeft">
			<view class="tab-bar">
				<view v-for="(item, index) in (Array.isArray(tabItems) ? tabItems : (tabItems?.value || ['我的待办', '我的已办']))" :key="index" class="tab-item"
					@click="switchTab(index)" :ref="el => {
						// 容错处理：确保tabItems和tabItemRefs都是有效的
						if (!tabItems || !tabItems.value) {
							tabItems.value = ['我的待办', '我的已办'];
						}

						// 容错处理：确保tabItemRefs.value是一个数组
						if (!Array.isArray(tabItemRefs.value)) {
							tabItemRefs.value = new Array(Array.isArray(tabItems.value) ? tabItems.value.length : 2);
						}
						// 设置引用
						if (el) tabItemRefs.value[index] = el
					}">
					{{ item }}
				</view>
				<!-- 底部滑动条 -->
				<view ref="tabLine" class="tab-line" :style="lineStyle"></view>
			</view>
		</scroll-view>
		<!-- 列表 -->
		<swiper class="tab-content" :current="activeIndex" @change="onSwiperChange" :duration="300">
			<swiper-item v-for="(item, index) in (Array.isArray(tabItems) ? tabItems : (tabItems?.value || ['我的待办', '我的已办']))" :key="index">
				<view class="content">
					<scroll-view class="list-content" scroll-y style="overflow: auto;" @scrolltolower="scrollBottom()">
						<uni-section v-show="activeIndex == 0" v-for="(listReocrd, i) in listReocrds"
							:key="listReocrd.id" title="" class="list-item">
							<view>
								<uni-row class="list-column"
									style="color: #3C5176; font-size: .98em; display: flex; flex-wrap: nowrap; align-items: center; width: 100%;">
									<uni-col style="width: 69px; flex-shrink: 0;">
										<view class="label">工单编号：</view>
									</uni-col>
									<uni-col style="flex: 1; min-width: 0;">
										<view class="value"
											style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap;">
											{{ listReocrd.code || '-' }}</view>
									</uni-col>
									<view style="display: flex; flex-shrink: 0; margin-left: 5px;">
										<view style="margin-right: 5px;">
											<uni-tag :type="getLevelColor(listReocrd.orderLevel)"
												:text="getLevel(listReocrd.orderLevel) || '未知'"></uni-tag>
										</view>
										<view v-if="listReocrd.timeoutTag == 1">
											<uni-tag text="超时"></uni-tag>
										</view>
									</view>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrd)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">工单标题：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrd.title }}</view>
									</uni-col>
								</uni-row>
								<uni-row class="list-column">
									<uni-col :span="10" style="width: 100px;color: #10172A;">
										<view class="label">最后派单时间：</view>
									</uni-col>
									<uni-col :span="10" style="width: calc(100% - 100px);color: #3C5176;">
										<view class="value">{{ listReocrd.updateTime }}</view>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrd)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 60px;">
										<view class="label">创建人：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 140px);color: #3C5176;">
										<view class="value">{{ listReocrd.createBy || '-' }}</view>
									</uni-col>
									<uni-col style="width: 60px;text-align: right;"></uni-col>
								</uni-row>
							</view>
						</uni-section>

						<uni-section v-show="activeIndex == 1" v-for="(listReocrdsDone, i) in listReocrdsDones"
							:key="listReocrdsDone.id" title="" class="list-item">
							<view>
								<uni-row class="list-column" style="color: #3C5176; font-size: .98em;">
									<uni-col :span="9" style="width: 80px;">
										<view class="label">工单编号：</view>
									</uni-col>
									<uni-col :span="12" style="width: calc(100% - 80px - 44px);">
										<view class="value"
											style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
											{{ listReocrdsDone.code || '-' }}</view>
									</uni-col>
									<uni-col :span="3" style="width: 44px;">
										<uni-tag :type="getLevelColor(listReocrdsDone.orderLevel)"
											:text="getLevel(listReocrdsDone.orderLevel) || '未知'"></uni-tag>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrdsDone)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">工单标题：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.title }}</view>
									</uni-col>
								</uni-row>
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">处理时间：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.updateTime }}</view>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrdsDone)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;">
										<view class="label">创建人：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 140px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.createBy || '-' }}</view>
									</uni-col>
									<uni-col style="width: 60px;text-align: right;"></uni-col>
								</uni-row>
							</view>
						</uni-section>

						<view style="height: 20rpx;"></view>
						<template v-if="listReocrds.length == 0 && activeIndex == 0">
							<view class="empty-text">暂无数据</view>
						</template>
						<template v-if="listReocrdsDones.length == 0 && activeIndex == 1">
							<view class="empty-text">暂无数据</view>
						</template>

						<view style="height: 13rem;"></view>
					</scroll-view>
				</view>
			</swiper-item>
		</swiper>

		<view v-if="false" style="position: fixed;bottom: 0px; width: 100%;">
			<button type="primary" @click="showPop" style="margin-top: 20px;">创建工单</button>
		</view>

		<!-- 弹窗设计 -->
		<uni-popup class="popup" ref="popup" background-color="#fff">
			<uni-section title="选择流程">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 30px); overflow: auto;">
						<uni-col :span="24" v-for="(procItem, itemIndex) in procSelects" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<view class="process-type" @click="toDraftList(procItem)">
									<text :title="procItem.procName">{{ procItem.procName }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { onLoad, onShow, onBackPress } from '@dcloudio/uni-app'
import DaDropdown from '/components/da-dropdown_2/components/da-dropdown/index.vue'
import { queryTodoList, workOrderListWorkOrderLevel, claimTask, queryDoneList } from "./api/index.js"

// 工单上下文
const context = reactive({
	current: {}
})

// 状态
const dropdownMenuList = ref([
	{
		title: '筛选',
		type: 'filter',
		prop: 'conditions',
		options: [
			{
				title: '工单级别',
				type: 'radio',
				prop: 'alarmSeverity',
				options: [],
			},
			{
				title: '工单创建时间',
				type: 'radio',
				prop: 'alarmOccurrenceTime',
				options: [
					{ value: 30, label: "近一月" },
					{ value: 180, label: "近半年" },
					{ value: 360, label: "近一年" },
				],
			},
		],
	},
])

const searchForm = reactive({
	alarmSeverity: "",
	disposalState: "",
	alarmOccurrenceTime: ""
})

const rangSearchForm = reactive({
	alarmSeverity: [],
	disposalState: [
		{ value: 0, text: "未处置" },
		{ value: 1, text: "已处置" },
		{ value: 2, text: "流转中" },
	],
	alarmOccurrenceTime: [
		{ value: 30, text: "近一月" },
		{ value: 180, text: "近半年" },
		{ value: 360, text: "近一年" },
	]
})

const loadedIndexArray = ref([])
const activeIndex = ref(0)
const itemWidth = ref(0)
const lineWidth = ref(0)
const scrollLeft = ref(0)
const lineLeft = ref(0)
const searchValue = reactive({
	title: "",
	code: "",
})

const tabItems = ref(["我的待办", "我的已办"])
const typeValues = ref(["TODO", "TRACK", "JOIN"])
const currentTabIndex = ref(0)

const pageNum = ref(1)
const pageSize = ref(20)
const pageNumDone = ref(1)
const pageSizeDone = ref(20)
const totalList = ref(0)
const totalListDone = ref(0)
const listReocrds = ref([])
const listReocrdsDones = ref([])

const selectProcKey = ref(null)
const procSelects = ref([])
const title = ref("我的工单")
const orderLevelArray = ref([])
const theme = ref(false)

// DOM引用
const popup = ref(null)
const tabScroll = ref(null)
const tabLine = ref(null)
const tabItemRefs = ref([])

// 计算属性
const lineStyle = computed(() => {
	let style = {};

	// #ifdef APP-PLUS
	// 计算每个tab的宽度（假设两个tab平分屏幕宽度）
	const systemInfo = uni.getSystemInfoSync();
	const screenWidth = systemInfo.windowWidth;
	const tabWidth = screenWidth / tabItems.value.length;

	// 计算蓝色条的宽度和位置
	const appLineWidth = 80; // rpx
	const pxLineWidth = appLineWidth * (screenWidth / 750); // 转换为px

	style = {
		width: `${appLineWidth}rpx`,
		transform: `translateX(${activeIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
		transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
	};
	// #endif

	// #ifndef APP-PLUS
	style = {
		width: `${lineWidth.value}px`,
		transform: `translateX(${activeIndex.value * itemWidth.value + (itemWidth.value - lineWidth.value) / 2}px)`,
		transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
	};
	// #endif

	return style;
})

const totalPage = computed(() => {
	let total = totalList.value
	let n = total % pageSize.value
	let totalPage = n == 0 ? total / pageSize.value : 1 + (total - n) / pageSize.value
	return totalPage == 0 ? 1 : totalPage
})

const totalPageDone = computed(() => {
	let total = totalListDone.value
	let n = total % pageSizeDone.value
	let totalPage = n == 0 ? total / pageSizeDone.value : 1 + (total - n) / pageSizeDone.value
	return totalPage == 0 ? 1 : totalPage
})

// 方法
const handleConfirm = (v, selectedValue) => {
	if (selectedValue.conditions.alarmSeverity) {
		searchForm.alarmSeverity = selectedValue.conditions.alarmSeverity
	} else {
		searchForm.alarmSeverity = ''
	}
	if (selectedValue.conditions.alarmOccurrenceTime) {
		searchForm.alarmOccurrenceTime = selectedValue.conditions.alarmOccurrenceTime
	} else {
		searchForm.alarmOccurrenceTime = ''
	}
}

const handleClose = () => { }
const handleOpen = () => { }

const getLevelColor = (type) => {
	switch (type) {
		case 'EMER':
			return 'error'
		case 'MAJOR':
			return 'warning'
		case 'GENERIC':
			return 'primary'
		default:
			return 'info'
	}
}

const getLevel = (type) => {
	let level = ''
	orderLevelArray.value?.forEach(item => {
		if (item.value == type) {
			level = item.label
		}
	})
	return level
}

const getMountedNeedLoadData = async (appendFlag, isClear) => {
	listReocrds.value = []
	listReocrdsDones.value = []
	uni.showLoading({
		title: "加载中...",
		icon: "loading"
	})
	try {
		const res = await workOrderListWorkOrderLevel()
		rangSearchForm.alarmSeverity = []
		orderLevelArray.value = res.data
		res.data.forEach(item => {
			rangSearchForm.alarmSeverity.push({ ...item, text: item.label })
		})
		dropdownMenuList.value[0]['options'][0]['options'] = rangSearchForm.alarmSeverity
		refresh(appendFlag, isClear)
	} finally {
		uni.hideLoading()
	}
}

const calcTabWidth = () => {
	// #ifdef APP-PLUS
	const dom = uni.requireNativePlugin('dom')

	// 容错处理：确保tabItems和tabItemRefs都是有效的
	if (!tabItems || !tabItems.value) {
		tabItems.value = ['我的待办', '我的已办'];
	}

	// 容错处理：确保tabItemRefs.value是一个数组
	if (!Array.isArray(tabItemRefs.value)) {
		tabItemRefs.value = new Array(Array.isArray(tabItems.value) ? tabItems.value.length : 2);
		// 使用默认值
		itemWidth.value = 166
		lineWidth.value = 133
		return;
	}

	// 获取当前激活的tab项的引用
	const tabItemRef = tabItemRefs.value[activeIndex.value]
	if (tabItemRef) {
		dom.getComponentRect(tabItemRef, res => {
			console.log('APP端元素尺寸:', res)
			if (res?.size?.width) {
				itemWidth.value = res.size.width
				lineWidth.value = res.size.width * 0.8
			} else {
				// 如果获取失败，使用默认值
				itemWidth.value = 166
				lineWidth.value = 133
			}
		})
	} else {
		// 如果引用不存在，使用默认值
		itemWidth.value = 166
		lineWidth.value = 133
	}
	// #endif

	// #ifndef APP-PLUS
	const query = uni.createSelectorQuery()
	query.select('.tab-item').boundingClientRect(res => {
		if (res) {
			itemWidth.value = res.width
			lineWidth.value = res.width * 0.8
		}
	}).exec()
	// #endif
}

const adjustScrollPosition = () => {
	let offset = 0;
	const systemInfo = uni.getSystemInfoSync();

	// #ifdef APP-PLUS
	// 获取屏幕宽度
	const screenWidth = systemInfo.windowWidth;

	// 计算每个tab的宽度
	const tabWidth = screenWidth / tabItems.value.length;

	// 计算滚动偏移量
	offset = activeIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
	offset = Math.max(0, offset);

	// 设置滚动位置
	nextTick(() => {
		if (tabScroll.value && typeof tabScroll.value.setScrollLeft === 'function') {
			tabScroll.value.setScrollLeft({
				scrollLeft: offset,
				duration: 300
			});
		}
	});
	// #else
	offset = activeIndex.value * itemWidth.value - systemInfo.windowWidth / 2 + itemWidth.value / 2;
	scrollLeft.value = Math.max(0, offset);
	// #endif
}

const switchTab = (index) => {
	currentTabIndex.value = index
	activeIndex.value = index
	adjustScrollPosition()
}

const onSwiperChange = (e) => {
	activeIndex.value = e.detail.current
	currentTabIndex.value = e.detail.current
	adjustScrollPosition()
}

const clearSearch = () => {
	searchValue.title = ""
	searchValue.code = ""
	refresh(false, true)
}

const refresh = (appendFlag, isClear) => {
	pageNum.value = 1
	totalList.value = 0
	pageNumDone.value = 1
	totalListDone.value = 0
	queryPageLists(appendFlag, isClear)
}

const scrollBottom = () => {
	if (activeIndex.value == 0) {
		if (pageNum.value < totalPage.value) {
			pageNum.value++
			queryPageLists(true)
		} else {
			uni.showToast({
				title: "没有更多数据了"
			})
		}
	} else if (activeIndex.value == 1) {
		if (pageNumDone.value < totalPageDone.value) {
			pageNumDone.value++
			queryPageLists(true)
		} else {
			uni.showToast({
				title: "没有更多数据了"
			})
		}
	}
}

const search = () => {
	pageNum.value = 1
	nextTick(() => queryPageLists(false, true))
}

const queryPageLists = async (appendFlag, isClear) => {
	uni.showLoading({
		title: "加载中...",
		icon: "loading"
	})
	try {
		if (activeIndex.value == 0 || isClear) {
			const params = {
				pageSize: pageSize.value,
				pageNum: pageNum.value,
				latestDay: searchForm.alarmOccurrenceTime,
				code: searchValue.code,
				title: searchValue.title,
				orderLevel: searchForm.alarmSeverity
			}
			const res = await queryTodoList(params)
			loadedIndexArray.value.push(currentTabIndex.value)
			const total = res.total
			const list = res.data
			totalList.value = total || 0
			const records = list || []
			if (appendFlag) {
				listReocrds.value.push(...records)
			} else {
				listReocrds.value = records
			}
		}
		if (activeIndex.value == 1 || isClear) {
			const params = {
				pageSize: pageSizeDone.value,
				pageNum: pageNumDone.value,
				latestDay: searchForm.alarmOccurrenceTime,
				code: searchValue.code,
				title: searchValue.title,
				orderLevel: searchForm.alarmSeverity
			}
			const res = await queryDoneList(params)
			loadedIndexArray.value.push(currentTabIndex.value)
			const total = res.total
			const list = res.data
			totalListDone.value = total || 0
			const records = list || []
			if (appendFlag) {
				listReocrdsDones.value.push(...records)
			} else {
				listReocrdsDones.value = records
			}
		}
	} finally {
		uni.hideLoading()
	}
}

const showPop = () => {
	popup.value.open("bottom")
}

const closePop = () => {
	popup.value.close()
}

const handleClaimTask = async (listReocrd) => {
	const { procKey, mainId, taskId } = listReocrd
	uni.showLoading({
		title: "正在受理...",
		icon: "loading"
	})
	try {
		const res = await claimTask(procKey, mainId, taskId)
		if (!res.data || res.data.status == '0') {
			uni.showToast({
				title: "受理成功",
				duration: 500
			})
			setTimeout(() => {
				refresh()
			}, 500)
		} else {
			uni.showToast({
				title: "受理失败"
			})
		}
	} catch (err) {
		uni.showToast({
			title: "受理失败"
		})
	} finally {
		uni.hideLoading()
	}
}

// 获取流程选择列表
const getProcSelects = async () => {
	try {
		// 这里应该调用获取流程列表的API
		// 由于原代码中没有提供具体的API，这里只是一个示例实现
		procSelects.value = []
	} catch (error) {
		console.error('获取流程列表失败:', error)
		uni.showToast({
			title: '获取流程列表失败',
			icon: 'none'
		})
	}
}

const toDraftList = (procItem) => {
	uni.navigateTo({
		url: "/pages/list/draft_list?procKey=" + procItem.procKey + "&procName=" + procItem.procName
	})
	closePop()
}

const toDealTodo = (listRecord) => {
	context.current = listRecord
	if (currentTabIndex.value == 0) {
		uni.navigateTo({
			url: `/pages/list/deal_list?definitionId=${listRecord.definitionId}&nowNodeCode=${listRecord.nodeCode}&nodeName=${listRecord.nodeName}&id=${listRecord.businessId}&businessType=${listRecord.businessType}`
		})
	} else {
		uni.navigateTo({
			url: `/pages/list/detail_list?definitionId=${listRecord.definitionId}&nowNodeCode=${listRecord.nodeCode}&nodeName=${listRecord.nodeName}&id=${listRecord.businessId}&businessType=${listRecord.businessType}`
		})
	}
}

const showListDetail = (listRecord) => {
	context.current = listRecord
	uni.navigateTo({
		url: "/pages/list/view_list"
	})
}

// 生命周期钩子
onLoad((page) => {
	currentTabIndex.value = Number(page.index || 0)
	activeIndex.value = Number(page.index || 0)
	if (page.index == 0) {
		title.value = "待办工单"
	} else if (page.index == 1) {
		title.value = "已办工单"
	}
	uni.setNavigationBarTitle({
		title: title.value
	})
	getProcSelects()
})

onShow(() => {
	getMountedNeedLoadData(false, true)
	theme.value = uni.getStorageSync('theme') || false
	if (theme.value) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		})
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		})
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		})
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		})
	}
})

onBackPress(() => {
	uni.hideLoading()
})

onMounted(() => {
	// 容错处理：确保tabItems是有效的
	if (!tabItems || !tabItems.value) {
		tabItems.value = ['我的待办', '我的已办'];
	}

	// 初始化tabItemRefs数组，确保有足够的元素
    tabItemRefs.value = new Array(Array.isArray(tabItems.value) ? tabItems.value.length : 2)

	calcTabWidth()
	theme.value = uni.getStorageSync('theme') || false
	if (theme.value) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		})
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		})
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		})
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		})
	}
})

// 监听器
watch(currentTabIndex, (newVal, oldVal) => {
	if (newVal != oldVal) {
		if (loadedIndexArray.value.includes(newVal)) {
			return
		}
		refresh()
	}
}, { immediate: true })

watch(activeIndex, (newVal) => {
	if (newVal == 0) {
		title.value = "待办工单"
	} else if (newVal == 1) {
		title.value = "已办工单"
	}
	uni.setNavigationBarTitle({
		title: title.value
	})
}, { immediate: true })

watch(() => searchForm.alarmOccurrenceTime, (newVal) => {
	if (newVal == 30) {
		searchForm.alarmOccurrenceTime = 30
	} else if (newVal == 180) {
		searchForm.alarmOccurrenceTime = 180
	} else if (newVal == 360) {
		searchForm.alarmOccurrenceTime = 360
	}
	pageNum.value = 1
	totalList.value = 0
	pageNumDone.value = 1
	totalListDone.value = 0
	listReocrdsDones.value = []
	listReocrds.value = []
	queryPageLists(false, true)
}, { deep: true })

watch(() => searchForm.alarmSeverity, () => {
	pageNum.value = 1
	totalList.value = 0
	pageNumDone.value = 1
	totalListDone.value = 0
	listReocrdsDones.value = []
	listReocrds.value = []
	queryPageLists(false, true)
}, { deep: true })

watch(theme, (newVal) => {
	uni.setStorageSync('theme', newVal)
	if (newVal) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		})
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		})
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		})
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		})
	}
})
</script>

<style lang="scss">
/* 全局样式，禁用外层滚动 */
:deep(html),
:deep(body) {
  overflow: hidden !important;
  height: 100% !important;
  position: fixed !important;
  width: 100% !important;
}
</style>

<style lang="scss" scoped>
.my_list {
	font-size: 13pt;
	overflow: visible !important;
	/* 动态计算高度，适配不同终端 */
	/* #ifdef H5 */
	height: calc(100vh - var(--window-top));
	/* #endif */
	/* #ifdef APP-PLUS */
	height: calc(100vh - var(--status-bar-height) + 100px);
	/* #endif */
	/* #ifdef MP */
	height: calc(100vh - var(--status-bar-height) - 44px);
	/* #endif */
	display: flex;
	flex-direction: column;
	position: relative;

	.fixed-header {
		position: fixed;
		/* 动态计算顶部位置，适配不同终端 */
		/* #ifdef H5 */
		top: var(--window-top);
		/* #endif */
		/* #ifdef APP-PLUS */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		top: calc(var(--status-bar-height) + 44px);
		/* #endif */
		left: 0;
		right: 0;
		z-index: 100;
		background-color: #fff;
		/* 搜索区域高度 + Tab区域高度 */
		height: 84px;
	}

	.search-bar {
		display: flex;
		align-items: center;
		background-color: #fff;
	}

	.list-searchbar {
		padding: 16rpx 26rpx 6rpx 26rpx;

		:deep(.uni-searchbar__box) {
			justify-content: unset !important;
		}

		:deep(.uni-searchbar__box) {
			background-color: #fff !important;
			border: 2rpx solid #eee;
		}
	}

	.tabs {
		background-color: #fff;
		padding: 3px;
		margin-top: -6px;

		:deep(.segmented-control__item--text) {
			border-style: unset;
		}
	}

	.list-content {
		/* 动态计算高度，适配不同终端 */
		/* #ifdef H5 */
		height: calc(100vh - var(--window-top) - 84px);
		/* #endif */
		/* #ifdef APP-PLUS */
		height: calc(100vh - var(--status-bar-height) + 100px);
		/* #endif */
		/* #ifdef MP */
		height: calc(100vh - var(--status-bar-height) - 44px - 84px);
		/* #endif */
		padding-bottom: 0;
		overflow: visible !important;

		.empty-text {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: gray;
		}

		.list-item {
			margin: 5px 10px 10px;
			padding: 10px 20px;
			font-size: 13pt;
			border-radius: 16rpx;
			border: 2rpx solid #ddd;

			.split-line {
				height: 1px;
				background-color: #E3E8F0;
				transform: scaleY(.7);
				margin: 8px 0;
			}

			:deep(.uni-section-header) {
				display: none;
			}

			.list-title {
				display: flex;
				align-items: center;
				margin-bottom: 10pt;
			}

			.list-column {
				line-height: 25px;
			}

			.list-column {

				line-height: 25px;

			}

			.label {

				color: #10172A;

				font-size: 0.9em;

			}

			.value {

				color: #3C5176;

				font-size: 0.9em;

			}

		}

	}

	.tab-scroll {

		background-color: #fff;

		height: 40px;

		white-space: nowrap;

		position: relative;

	}

	.tab-bar {

		display: flex;

		position: relative;

		height: 100%;

	}

	.tab-item {

		flex: 1;

		display: inline-block;

		text-align: center;

		line-height: 40px;

		font-size: 14px;

		color: #333;

		position: relative;

		min-width: 80px;

	}

	.tab-line {
		position: absolute;
		bottom: 0;
		height: 2px;
		background-color: #007aff;
		transition: all 0.3s;
	}

	.tab-content {
		flex: 1;
		background-color: #f5f5f5;
		overflow: hidden;
	}

	/* 内容区域 */
	swiper {
		flex: 1;
		/* 动态计算高度，适配不同终端 */
		/* #ifdef H5 */
		height: calc(100vh - var(--window-top) - 84px);
		/* #endif */
		/* #ifdef APP-PLUS */
		height: calc(100vh - var(--status-bar-height) + 100px);
		/* #endif */
		/* #ifdef MP */
		height: calc(100vh - var(--status-bar-height) - 44px - 84px);
		/* #endif */
		overflow: visible !important; /* 允许内容溢出显示 */
		touch-action: auto; /* 允许触摸滚动 */
		display: flex;
		flex-direction: column;
		position: relative;
		z-index: 1; /* 确保swiper在正确的层级 */
	}

	swiper-item {
		overflow: visible !important;
		touch-action: auto; /* 允许触摸滚动 */
	}

	.content {
		height: 100%;
		overflow: visible !important;
		touch-action: auto; /* 允许触摸滚动 */
	}

	.popup {

		:deep(.uni-popup__wrapper) {

			height: 60%;

		}

		.popup-content {

			height: 100%;

			overflow: auto;

		}

		.process-type {

			padding: 10px;

			border-bottom: 1px solid #eee;

			font-size: 14px;

		}

	}

	.search-bar-dropdown {

		:deep(.da-dropdown) {

			height: 100%;

			margin-top: 0;

			margin-right: 0;

			margin-left: 0;

			padding-top: 0;

			padding-right: 0;

			padding-left: 0;

			padding-bottom: 0;

		}

		:deep(.da-dropdown__menu) {

			height: 100%;

			margin-top: 0;

			margin-right: 0;

			margin-left: 0;

			padding-top: 0;

			padding-right: 0;

			padding-left: 0;

			padding-bottom: 0;

		}

		:deep(.da-dropdown__menu-item) {

			height: 100%;

			margin-top: 0;

			margin-right: 0;

			margin-left: 0;

			padding-top: 0;

			padding-right: 0;

			padding-left: 0;

			padding-bottom: 0;

		}

		:deep(.da-dropdown__menu-item__text) {

			height: 100%;

			margin-top: 0;

			margin-right: 0;

			margin-left: 0;

			padding-top: 0;

			padding-right: 0;

			padding-left: 0;

			padding-bottom: 0;

		}

	}

}

.my_list-dark {
	background-color: #2b2b2b;
	color: #fff;

	.fixed-header {
		background-color: #2b2b2b;
	}

	.search-bar {
		background-color: #2b2b2b;

		:deep(.search-bar-dropdown) {
			.da-dropdown-menu-item--text span {
				color: #fff;
			}
		}

		:deep(.uni-input-input) {
			color: #fff;
		}
	}

	.list-searchbar {

		:deep(.uni-searchbar__box) {
			background-color: #2b2b2b !important;
			border: 2rpx solid #444;

			span {
				color: #fff;
			}
		}

		:deep(.uni-searchbar__text-input) {
			color: #fff;
		}

	}

	.tab-scroll {

		background-color: #2b2b2b;

	}

	.tab-item {

		color: #fff;

	}

	.tab-content {

		background-color: #2b2b2b;

	}

	.list-content {
		.list-item {
			background-color: #333;
			border: 2rpx solid #444;

			.split-line {
				background-color: #444;
			}

			.label {
				color: #ccc;
			}

			.value {
				color: #fff;
			}
		}
	}

	:deep(.da-dropdown-filter) {
		background-color: #2b2b2b !important;

		.da-dropdown-filter--title {
			color: #fff !important;
		}

		border-bottom: 1px solid #ffffff89 !important;
	}

	:deep(.search-bar) {
		.uni-searchbar__box {
			border-color: #ffffff89;
		}
	}

	.uni-scroll-view-content {
		.list-item {
			border-color: #ffffff89;
		}
	}

	:deep(.da-dropdown-menu-item--text){
		color: #fff;

	}

}
</style>