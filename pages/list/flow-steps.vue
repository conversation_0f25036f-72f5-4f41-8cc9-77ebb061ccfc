<template>
	<view class="flow-steps">
		<view :class="[direction==='column'?'flow-steps__column':'flow-steps__row']">
			<view :class="[direction==='column'?'flow-steps__column-text-container':'flow-steps__row-text-container']">
			
				<view v-for="(item,index) in options" :key="index"
					:class="[direction==='column'?'flow-steps__column-text':'flow-steps__row-text']">
					<slot name="item" v-bind="{item, index, isLastItem: index == options.length - 1}">
						<text :style="{color:index === active?activeColor:deactiveColor}"
							:class="[direction==='column'?'flow-steps__column-title':'flow-steps__row-title']">{{item.title}}</text>
						<text :style="{color: deactiveColor}"
							:class="[direction==='column'?'flow-steps__column-desc':'flow-steps__row-desc']">{{item.desc}}</text>	
					</slot>
				</view>
			</view>
			<view :class="[direction==='column'?'flow-steps__column-container':'flow-steps__row-container']">
				<view :class="[direction==='column'?'flow-steps__column-line-item':'flow-steps__row-line-item']"
					v-for="(item,index) in options" :key="index">
					<view
						:class="[direction==='column'?'flow-steps__column-line':'flow-steps__row-line',direction==='column'?'flow-steps__column-line--before':'flow-steps__row-line--before']"
						:style="{backgroundColor:index<=active&&index!==0?activeColor:index===0?'transparent':deactiveColor}">
					</view>
					<view :class="[direction==='column'?'flow-steps__column-check':'flow-steps__row-check']"
						v-if="index === active">
						<uni-icons :color="activeColor" :type="activeIcon" size="14"></uni-icons>
					</view>
					<view v-else :class="[direction==='column'?'flow-steps__column-circle':'flow-steps__row-circle']"
						:style="{backgroundColor:index<active?activeColor:deactiveColor}"></view>
					<view
						:class="[direction==='column'?'flow-steps__column-line':'flow-steps__row-line',direction==='column'?'flow-steps__column-line--after':'flow-steps__row-line--after']"
						:style="{backgroundColor:index<active&&index!==options.length-1?activeColor:index===options.length-1?'transparent':deactiveColor}">
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Steps 步骤条
	 * @description 评分组件
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=34
	 * @property {Number} active 当前步骤
	 * @property {String} direction = [row|column] 当前步骤
	 * 	@value row 横向
	 * 	@value column 纵向
	 * @property {String} activeColor 选中状态的颜色
	 * @property {Array} options 数据源，格式为：[{title:'xxx',desc:'xxx'},{title:'xxx',desc:'xxx'}]
	 */

	export default {
		name: 'FlowSteps',
		props: {
			direction: {
				// 排列方向 row column
				type: String,
				default: 'row'
			},
			activeColor: {
				// 激活状态颜色
				type: String,
				default: '#2979FF'
			},
			deactiveColor: {
				// 未激活状态颜色
				type: String,
				default: '#B7BDC6'
			},
			active: {
				// 当前步骤
				type: Number,
				default: 0
			},
			activeIcon: {
				// 当前步骤
				type: String,
				default: 'checkbox-filled'
			},
			options: {
				type: Array,
				default () {
					return []
				}
			} // 数据
		},
		data() {
			return {}
		}
	}
</script>

<style lang="scss">
	$uni-primary: #2979ff !default;
	$uni-border-color:#EDEDED;
	.flow-steps {
		/* #ifndef APP-NVUE */
		display: flex;
		width: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
		flex-direction: column;
	}

	.flow-steps__row {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
	}

	.flow-steps__column {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row-reverse;
	}

	.flow-steps__row-text-container {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: flex-end;
		margin-bottom: 8px;
	}

	.flow-steps__column-text-container {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		flex: 1;
	}

	.flow-steps__row-text {
		/* #ifndef APP-NVUE */
		display: inline-flex;
		/* #endif */
		flex: 1;
		flex-direction: column;
	}

	.flow-steps__column-text {
		padding: 6px 0px;
		border-bottom-style: solid;
		border-bottom-width: 1px;
		border-bottom-color: $uni-border-color;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
	}

	.flow-steps__row-title {
		font-size: 14px;
		line-height: 16px;
		text-align: center;
	}

	.flow-steps__column-title {
		font-size: 14px;
		text-align: left;
		line-height: 18px;
	}

	.flow-steps__row-desc {
		font-size: 12px;
		line-height: 14px;
		text-align: center;
	}

	.flow-steps__column-desc {
		font-size: 12px;
		text-align: left;
		line-height: 18px;
	}

	.flow-steps__row-container {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	.flow-steps__column-container {
		/* #ifndef APP-NVUE */
		display: inline-flex;
		/* #endif */
		width: 30px;
		flex-direction: column;
	}

	.flow-steps__row-line-item {
		/* #ifndef APP-NVUE */
		display: inline-flex;
		/* #endif */
		flex-direction: row;
		flex: 1;
		height: 14px;
		line-height: 14px;
		align-items: center;
		justify-content: center;
	}

	.flow-steps__column-line-item {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.flow-steps__row-line {
		flex: 1;
		height: 1px;
		background-color: #B7BDC6;
	}

	.flow-steps__column-line {
		width: 1px;
		background-color: #B7BDC6;
	}

	.flow-steps__row-line--after {
		transform: translateX(1px);
	}

	.flow-steps__column-line--after {
		flex: 1;
		transform: translate(0px, 1px);
	}

	.flow-steps__row-line--before {
		transform: translateX(-1px);
	}

	.flow-steps__column-line--before {
		height: 6px;
		transform: translate(0px, -13px);
	}

	.flow-steps__row-circle {
		width: 5px;
		height: 5px;
		border-radius: 50%;
		background-color: #B7BDC6;
		margin: 0px 3px;
	}

	.flow-steps__column-circle {
		width: 5px;
		height: 5px;
		border-radius: 50%;
		background-color: #B7BDC6;
		margin: 4px 0px 5px 0px;
	}

	.flow-steps__row-check {
		margin: 0px 6px;
	}

	.flow-steps__column-check {
		height: 14px;
		line-height: 14px;
		margin: 2px 0px;
	}
</style>
