<template>
    <view class="container" style="background-color: #fff;padding: 16rpx;height: 100vh;">
        <view>处置进度-流程图</view>
        <view style="height: 666px;">
            <!-- <view style="font-size: 53rpx;color: #999;font-weight: 300;">流程图</view> -->
            <l-echart ref="flowChartContainer"></l-echart>
        </view>
    </view>
</template>

<script>
import * as echarts from 'echarts';

export default {
    mounted() {
        this.initFlowChart();
    },
    methods: {
        async initFlowChart() {

            const chart = await this.$refs.flowChartContainer.init(echarts);
            const option = {
                series: [{
                    type: 'graph',
                    layout: 'none', // 手动控制节点坐标
                    symbolSize: [120, 60], // 矩形节点尺寸
                    edgeSymbol: ['circle', 'arrow'], // 连线端点样式
                    edgeSymbolSize: [0, 10], // 箭头大小
                    label: {
                        show: true,
                        position: 'inside',
                        color: '#fff',
                        fontSize: 13
                    },
                    itemStyle: {
                        color: '#73d13d',
                        borderColor: '#237804',
                        borderWidth: 2
                    },
                    // 自定义节点样式
                    nodes: [
                        {
                            id: 'startNode',
                            x: 16,
                            y: 40,
                            symbol: 'circle', // 圆形节点
                            symbolSize: 60,
                            label: {
                                formatter: '启动',
                                position: 'inside'
                            }
                        },
                        {
                            id: 'qicao',
                            x: 16,
                            y: 100,
                            symbol: 'Rect', // 圆角矩形
                            symbolSize: [120, 60],
                            label: {
                                formatter: '起草工单',
                                position: 'inside'
                            }
                        },
                        {
                            id: 'chuzhi',
                            x: 16,
                            y: 200,
                            symbol: 'Rect',
                            symbolSize: [120, 60],
                            label: {
                                formatter: '处置',
                                position: 'inside'
                            }
                        }
                    ],
                    // 连线配置
                    edges: [
                        {
                            source: 'startNode',
                            target: 'qicao',
                            label: {
                                show: true,
                                position: 'middle',
                                formatter: '新建'
                            }
                        },
                        {
                            source: 'qicao',
                            target: 'chuzhi',
                            label: {
                                show: true,
                                position: 'middle',
                                formatter: '处置'
                            }
                        },
                        {
                            source: 'chuzhi',
                            target: 'qicao',
                            label: {
                                show: true,
                                position: 'middle',
                                formatter: '退回'
                            },
                            lineStyle: {
                                type: 'dashed', // 虚线效果
                                curveness: -0.5 // 反向弯曲实现折线效果
                            }
                        }
                    ]
                }]
            };
            chart.setOption(option);
            // const chart = echarts.init(this.$refs.flowChartContainer);
            // chart.setOption(option);
        }
    }
};
</script>