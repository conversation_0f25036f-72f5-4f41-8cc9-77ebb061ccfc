<template>
    <view class="statistics-container" :class="{ 'statistics-container-dark': theme }">
        <view class="content-wrapper">
            <!-- 顶部区域 -->
            <view class="header-section">
                <view class="header-title">工单统计分析</view>
                <!-- 时间筛选器 -->
                <view class="time-filter">
                    <text class="filter-item" :class="{ active: activeTimeFilter === 'week' }" @click="setTimeFilter('week')">周</text>
                    <text class="filter-item" :class="{ active: activeTimeFilter === 'month' }" @click="setTimeFilter('month')">月</text>
                </view>
            </view>

            <!-- 数据概览卡片 -->
            <view class="data-overview">
                <view class="data-card processing-card">
                    <view class="card-value processing">{{ doingCount }}</view>
                    <view class="card-label">处理中</view>
                    <view class="card-indicator processing-indicator"></view>
                </view>
                <view class="data-card resolved-card">
                    <view class="card-value resolved">{{ doneCount }}</view>
                    <view class="card-label">已完成</view>
                    <view class="card-indicator resolved-indicator"></view>
                </view>
                <view class="data-card timeout-card">
                    <view class="card-value timeout">{{ timeoutCount }}</view>
                    <view class="card-label">超时</view>
                    <view class="card-indicator timeout-indicator"></view>
                </view>
            </view>

            <!-- 工单状态统计 -->
            <view class="chart-section">
                <view class="section-header">
                    <text class="section-title">工单状态统计</text>
                </view>
                <view class="chart-container">
                    <l-echart ref="chart"></l-echart>
                </view>
            </view>

            <!-- 工单级别统计 -->
            <view class="chart-section">
                <view class="section-header">
                    <text class="section-title">工单级别统计</text>
                </view>
                <view class="data-overview mini-cards">
                    <view class="data-card mini emer-card">
                        <view class="card-value-lg emer">{{ emerCount }}</view>
                        <view class="card-label">紧急</view>
                        <view class="card-indicator emer-indicator"></view>
                    </view>
                    <view class="data-card mini major-card">
                        <view class="card-value-lg major">{{ majorCount }}</view>
                        <view class="card-label">重要</view>
                        <view class="card-indicator major-indicator"></view>
                    </view>
                    <view class="data-card mini generic-card">
                        <view class="card-value-lg generic">{{ genericCount }}</view>
                        <view class="card-label">一般</view>
                        <view class="card-indicator generic-indicator"></view>
                    </view>
                </view>
                <view class="chart-container">
                    <l-echart ref="chart02"></l-echart>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import * as echarts from 'echarts'
import {
    workorderStatistics,
    workorderTrend,
    workorderLevelTrend,
    workorderLevelSummary
} from './api/statistics.js'
import { checkCurrentPagePermission } from '../../common/permission-router.js'
export default {
    data() {
        return {
            activeTimeFilter: 'week', // 默认选中"周"
            timeFilterValue: 7, // 默认为周对应的7天
            theme: false, // 深色模式标志
            doingCount: '0', // 当前处理中的工单数量
            doneCount: '0', // 当前已解决的工单数量
            timeoutCount: '0', // 当前超时的工单数量
            emerCount: '0', // 紧急工单数量
            majorCount: '0', // 重要工单数量
            genericCount: '0' // 一般工单数量
        }
    },
    onLoad() {
        // 检查页面访问权限
        if (!checkCurrentPagePermission('/pages/list/workOrderStatistics')) {
            return; // 如果没有权限，中止页面加载
        }

        // 页面加载时的逻辑
    },
    onShow() {
        // 检查页面访问权限
        if (!checkCurrentPagePermission('/pages/list/workOrderStatistics')) {
            return; // 如果没有权限，中止页面显示
        }

        // 从本地存储获取主题设置
        this.theme = uni.getStorageSync('theme') || false;
        this.updateNavigationBarStyle();
    },
    computed: {
        // 计算属性
    },
    mounted() {
        this.init();

        // 从本地存储获取主题设置
        this.theme = uni.getStorageSync('theme') || false;
        this.updateNavigationBarStyle();
    },
    onBackPress() {
        // 返回按钮处理
    },
    watch: {
        // 监听主题变化
        theme(newVal) {
            uni.setStorageSync('theme', newVal);
            this.updateNavigationBarStyle();
        }
    },
    methods: {
        // 更新导航栏样式
        updateNavigationBarStyle() {
            if (this.theme) {
                uni.setNavigationBarColor({
                    frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#2b2b2b', // 背景颜色
                });
                uni.setTabBarStyle({
                    backgroundColor: '#2b2b2b',
                    color: '#ffffff',
                    selectedColor: '#fff'
                });
            } else {
                uni.setNavigationBarColor({
                    frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#ffffff', // 背景颜色
                });
                uni.setTabBarStyle({
                    backgroundColor: '#ffffff',
                    color: '#000000',
                    selectedColor: '#000'
                });
            }
        },

        // 设置时间筛选
        setTimeFilter(type) {
            this.activeTimeFilter = type;

            // 根据筛选类型设置对应的天数值
            if (type === 'week') {
                this.timeFilterValue = 7; // 周对应7天
            } else if (type === 'month') {
                this.timeFilterValue = 30; // 月对应30天
            }

            console.log('时间筛选值:', this.timeFilterValue, '天');

            // 根据筛选类型重新加载数据
            this.loadDataByTimeFilter();
        },

        // 根据时间筛选加载数据
        loadDataByTimeFilter() {
            // 这里可以根据 this.timeFilterValue 的值加载不同的数据
            // 例如可以传递天数参数到后端API
            console.log('加载最近', this.timeFilterValue, '天的数据');

            // 然后重新初始化图表
            this.init();
        },

        async init() {
            console.log('初始化图表，时间范围:', this.timeFilterValue, '天');

            // 获取工单统计数据
            try {
                const res = await workorderStatistics({ days: this.timeFilterValue });
                console.log('工单统计数据:', res);

                if (res.status === '0' && res.data) {
                    // 更新数据卡片的值
                    this.doingCount = res.data.doing || '0';
                    this.doneCount = res.data.done || '0';
                    this.timeoutCount = res.data.timeout || '0';

                    console.log('更新顶部卡片数据:', {
                        doing: this.doingCount,
                        done: this.doneCount,
                        timeout: this.timeoutCount
                    });
                }
            } catch (error) {
                console.error('获取工单统计数据失败:', error);
            }

            // 获取工单级别汇总数据
            try {
                const summaryRes = await workorderLevelSummary({ days: this.timeFilterValue });
                console.log('工单级别汇总数据:', summaryRes);

                if (summaryRes.status === '0' && summaryRes.data) {
                    // 更新数据卡片显示
                    this.emerCount = summaryRes.data.EMER || '0';
                    this.majorCount = summaryRes.data.MAJOR || '0';
                    this.genericCount = summaryRes.data.GENERIC || '0';
                }
            } catch (error) {
                console.error('获取工单级别汇总数据失败:', error);
            }

            // 获取工单级别趋势数据
            try {
                const trendRes = await workorderLevelTrend({ days: this.timeFilterValue });
                console.log('工单级别趋势数据:', trendRes);

                if (trendRes.status === '0' && trendRes.data) {
                    // 准备图表数据
                    this.prepareLevelChartData(trendRes.data);
                }
            } catch (error) {
                console.error('获取工单级别趋势数据失败:', error);
            }

            // 获取工单趋势数据
            let xAxisData = [];
            let doingData = [];
            let timeoutData = [];
            let doneData = [];

            try {
                const res = await workorderTrend({ days: this.timeFilterValue });
                console.log('工单趋势数据:', res);

                if (res.status === '0' && res.data) {
                    // 处理日期数据
                    if (res.data.dataX && res.data.dataX.length > 0) {
                        // 获取日期数据
                        let dateXData = [...res.data.dataX];
                        let dateData = [...res.data.date];

                        // 确保所有数据都被包含，特别是最后几天的数据
                        console.log('原始日期数据长度:', dateXData.length, dateData.length);

                        // 如果是周视图，只保留最近7天的数据
                        if (this.activeTimeFilter === 'week' && dateXData.length > 7) {
                            dateXData = dateXData.slice(-7);
                            dateData = dateData.slice(-7);
                        }

                        // 确保日期数据和日期标签长度一致
                        if (dateData.length !== dateXData.length) {
                            console.warn('日期数据和日期标签长度不一致:', dateData.length, dateXData.length);

                            // 检查是否有日期标签但没有对应的数据
                            const missingDates = dateXData.filter(date => !dateData.some(item => item.dayId === date));
                            if (missingDates.length > 0) {
                                console.warn('缺少数据的日期:', missingDates);
                                // 为缺少数据的日期添加空数据
                                missingDates.forEach(date => {
                                    dateData.push({
                                        dayId: date,
                                        doing: "0",
                                        done: "0",
                                        timeout: "0"
                                    });
                                });
                                // 按日期排序
                                dateData.sort((a, b) => new Date(a.dayId) - new Date(b.dayId));
                            }
                        }

                        // 格式化日期标签为 MM/DD 格式
                        xAxisData = dateXData.map(dateStr => {
                            const date = new Date(dateStr);
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            return `${month}/${day}`;
                        });

                        // 提取数据
                        doingData = dateData.map(item => parseInt(item.doing || 0));
                        timeoutData = dateData.map(item => parseInt(item.timeout || 0));
                        doneData = dateData.map(item => parseInt(item.done || 0));

                        // 确保数据长度与X轴标签长度一致
                        if (doingData.length < xAxisData.length) {
                            console.warn('数据长度小于X轴标签长度，填充缺失数据');
                            const diff = xAxisData.length - doingData.length;
                            for (let i = 0; i < diff; i++) {
                                doingData.push(0);
                                timeoutData.push(0);
                                doneData.push(0);
                            }
                        }

                        // 确保最后一天的数据存在
                        if (dateData.length > 0) {
                            const lastDay = dateData[dateData.length - 1];
                            console.log('最后一天数据:', lastDay);
                        }

                        console.log('图表X轴数据:', xAxisData, '长度:', xAxisData.length);
                        console.log('处理中数据:', doingData, '长度:', doingData.length);
                        console.log('超时数据:', timeoutData, '长度:', timeoutData.length);
                        console.log('已解决数据:', doneData, '长度:', doneData.length);

                        // 不再使用趋势数据更新顶部卡片，而是保留workorderStatistics接口返回的数据
                        const lastDayData = dateData[dateData.length - 1];
                        if (lastDayData) {
                            console.log('最后一天趋势数据:', {
                                doing: lastDayData.doing || '0',
                                done: lastDayData.done || '0',
                                timeout: lastDayData.timeout || '0'
                            });
                        }
                    } else {
                        // 如果没有日期数据，使用生成的日期标签
                        xAxisData = this.generateDateLabels(this.timeFilterValue);
                        doingData = Array(xAxisData.length).fill(0);
                        timeoutData = Array(xAxisData.length).fill(0);
                        doneData = Array(xAxisData.length).fill(0);
                    }
                } else {
                    // 如果API返回错误，使用默认数据
                    xAxisData = this.generateDateLabels(this.timeFilterValue);
                    doingData = Array(xAxisData.length).fill(0);
                    timeoutData = Array(xAxisData.length).fill(0);
                    doneData = Array(xAxisData.length).fill(0);
                }
            } catch (error) {
                console.error('获取工单趋势数据失败:', error);
                // 使用默认数据
                xAxisData = this.generateDateLabels(this.timeFilterValue);
                doingData = Array(xAxisData.length).fill(0);
                timeoutData = Array(xAxisData.length).fill(0);
                doneData = Array(xAxisData.length).fill(0);
            }

            // 工单状态统计图表
            this.$refs.chart.init(echarts, chart => {
                chart.setOption({
                    color: ['#FF9500', '#FF3B30', '#34C759'],
                    backgroundColor: this.theme ? '#333' : '#fff',
                    legend: {
                        data: ['处理中', '超时', '已完成'],
                        right: '5%',
                        icon: 'circle',
                        textStyle: {
                            fontSize: 12,
                            color: this.theme ? '#ccc' : '#666'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: this.theme ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                        borderColor: this.theme ? '#555' : '#f0f0f0',
                        borderWidth: 1,
                        textStyle: {
                            color: this.theme ? '#fff' : '#333'
                        },
                        axisPointer: {
                            type: 'line',
                            lineStyle: {
                                color: 'rgba(0, 122, 255, 0.2)',
                                width: 1
                            }
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: xAxisData,
                        boundaryGap: true,
                        // 确保X轴范围包含所有数据点
                        min: 0,
                        max: 'dataMax',
                        // 强制显示最后一个刻度
                        scale: true,
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false,
                            // 强制显示最后一个刻度
                            alignWithLabel: true
                        },
                        axisLabel: {
                            color: this.theme ? '#ccc' : '#999',
                            fontSize: 10,
                            // 自定义标签显示逻辑，确保最后一天始终显示
                            formatter: function(value, index) {
                                // 如果是最后一个标签或者符合间隔条件，则显示
                                if (index === xAxisData.length - 1 || index % Math.max(1, Math.floor(xAxisData.length / 10)) === 0) {
                                    return value;
                                }
                                return '';
                            },
                            // 如果是月视图且标签太多，旋转标签
                            rotate: this.activeTimeFilter === 'month' && xAxisData.length > 15 ? 45 : 0,
                            // 确保标签不会被裁剪
                            hideOverlap: false,
                            showMaxLabel: true
                        }
                    },
                    yAxis: {
                        type: 'value',
                        splitNumber: 4,
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: this.theme ? '#ccc' : '#999',
                            fontSize: 10
                        },
                        splitLine: {
                            lineStyle: {
                                color: this.theme ? '#444' : '#f5f5f5',
                                type: 'dashed'
                            }
                        }
                    },
                    grid: {
                        top: '15%',
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    // 确保显示所有数据点
                    animation: true,
                    animationDuration: 500,
                    animationEasing: 'cubicOut',
                    series: [
                        {
                            name: '处理中',
                            type: 'line',
                            data: doingData,
                            smooth: true,
                            emphasis: { focus: 'series' },
                            lineStyle: {
                                width: 3,
                                shadowColor: 'rgba(255, 149, 0, 0.3)',
                                shadowBlur: 10,
                                shadowOffsetY: 6
                            },
                            // 显示最后一个数据点的标记
                            showSymbol: true,
                            // 只在最后一个点显示标记
                            symbolSize: function(_, params) {
                                return params.dataIndex === doingData.length - 1 ? 8 : 0;
                            },
                            areaStyle: {
                                opacity: 0.1
                            }
                        },
                        {
                            name: '超时',
                            type: 'line',
                            data: timeoutData,
                            smooth: true,
                            emphasis: { focus: 'series' },
                            lineStyle: {
                                width: 3,
                                shadowColor: 'rgba(255, 59, 48, 0.3)',
                                shadowBlur: 10,
                                shadowOffsetY: 6
                            },
                            // 显示最后一个数据点的标记
                            showSymbol: true,
                            // 只在最后一个点显示标记
                            symbolSize: function(_, params) {
                                return params.dataIndex === timeoutData.length - 1 ? 8 : 0;
                            },
                            areaStyle: {
                                opacity: 0.1
                            }
                        },
                        {
                            name: '已完成',
                            type: 'line',
                            data: doneData,
                            smooth: true,
                            emphasis: { focus: 'series' },
                            lineStyle: {
                                width: 3,
                                shadowColor: 'rgba(52, 199, 89, 0.3)',
                                shadowBlur: 10,
                                shadowOffsetY: 6
                            },
                            // 显示最后一个数据点的标记
                            showSymbol: true,
                            // 只在最后一个点显示标记
                            symbolSize: function(_, params) {
                                return params.dataIndex === doneData.length - 1 ? 8 : 0;
                            },
                            areaStyle: {
                                opacity: 0.1
                            }
                        },
                    ]
                });
            });


        },

        // 生成日期标签
        generateDateLabels(days) {
            const labels = [];
            const today = new Date();

            // 添加日期标签，包括今天在内的days天
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);

                // 格式化日期为 MM/DD 格式
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                labels.push(`${month}/${day}`);
            }

            console.log(`生成了${labels.length}天的日期标签:`, labels);
            return labels;
        },





        // 准备工单级别趋势图表数据
        prepareLevelChartData(data) {
            // 初始化变量
            let dateLabels = [];
            let emerData = [];
            let majorData = [];
            let genericData = [];

            // 使用API返回的日期标签
            if (data.dataX && data.dataX.length > 0) {
                // 格式化日期标签为 MM/DD 格式
                dateLabels = data.dataX.map(dateStr => {
                    const date = new Date(dateStr);
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    return `${month}/${day}`;
                });

                // 初始化数据数组，默认值为0
                emerData = Array(dateLabels.length).fill(0);
                majorData = Array(dateLabels.length).fill(0);
                genericData = Array(dateLabels.length).fill(0);

                // 如果有日期数据
                if (data.date && data.date.length > 0) {
                    // 检查是否有日期标签但没有对应的数据
                    const missingDates = data.dataX.filter(date => !data.date.some(item => item.dayId === date));
                    if (missingDates.length > 0) {
                        console.warn('工单级别统计：缺少数据的日期:', missingDates);
                        // 为缺少数据的日期添加空数据
                        missingDates.forEach(date => {
                            data.date.push({
                                dayId: date,
                                EMER: "0",
                                MAJOR: "0",
                                GENERIC: "0"
                            });
                        });
                        // 按日期排序
                        data.date.sort((a, b) => new Date(a.dayId) - new Date(b.dayId));
                    }

                    // 填充实际数据
                    data.date.forEach(item => {
                        // 查找日期在dataX中的索引
                        const dateIndex = data.dataX.findIndex(d => d === item.dayId);
                        if (dateIndex !== -1) {
                            // 更新对应日期的数据
                            emerData[dateIndex] = parseInt(item.EMER || '0');
                            majorData[dateIndex] = parseInt(item.MAJOR || '0');
                            genericData[dateIndex] = parseInt(item.GENERIC || '0');
                        }
                    });

                    // 确保所有数据都被正确填充
                    console.log('工单级别统计数据:', {
                        dateLabels,
                        emerData,
                        majorData,
                        genericData
                    });
                }
            } else {
                // 如果没有日期标签，使用生成的日期标签
                dateLabels = this.generateDateLabels(this.timeFilterValue);
                emerData = Array(dateLabels.length).fill(0);
                majorData = Array(dateLabels.length).fill(0);
                genericData = Array(dateLabels.length).fill(0);
            }

            // 更新图表
            this.updateLevelChart(dateLabels, emerData, majorData, genericData);
        },

        // 更新工单级别图表
        updateLevelChart(dateLabels, emerData, majorData, genericData) {
            // 根据时间范围设置标题
            const titleText = this.activeTimeFilter === 'week' ? '近一周工单级别分布' : '近一月工单级别分布';

            this.$refs.chart02.init(echarts, chart => {
                chart.setOption({
                    backgroundColor: this.theme ? '#333' : '#fff',
                    color: ['#FF3B30', '#FF9500', '#34C759'], // 紧急、重要、一般
                    title: {
                        text: titleText,
                        left: 'center',
                        top: 0,
                        textStyle: {
                            fontSize: 14,
                            fontWeight: 'normal',
                            color: this.theme ? '#ccc' : '#666'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            shadowStyle: {
                                color: 'rgba(0, 0, 0, 0.03)'
                            }
                        },
                        backgroundColor: this.theme ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                        borderColor: this.theme ? '#555' : '#f0f0f0',
                        borderWidth: 1,
                        textStyle: {
                            color: this.theme ? '#fff' : '#333'
                        }
                    },
                    legend: {
                        data: ['紧急', '重要', '一般'],
                        top: '10%',
                        icon: 'circle',
                        itemWidth: 8,
                        itemHeight: 8,
                        textStyle: {
                            fontSize: 12,
                            color: this.theme ? '#ccc' : '#666'
                        }
                    },
                    grid: {
                        top: '20%',
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    // 确保显示所有数据点
                    animation: true,
                    animationDuration: 500,
                    animationEasing: 'cubicOut',
                    xAxis: [
                        {
                            type: 'category',
                            data: dateLabels,
                            // 确保X轴范围包含所有数据点
                            min: 0,
                            max: 'dataMax',
                            // 强制显示最后一个刻度
                            scale: true,
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false,
                                // 强制显示最后一个刻度
                                alignWithLabel: true
                            },
                            axisLabel: {
                                color: this.theme ? '#ccc' : '#999',
                                fontSize: 10,
                                // 自定义标签显示逻辑，确保最后一天始终显示
                                formatter: function(value, index) {
                                    // 如果是最后一个标签或者符合间隔条件，则显示
                                    if (index === dateLabels.length - 1 || index % Math.max(1, Math.floor(dateLabels.length / 10)) === 0) {
                                        return value;
                                    }
                                    return '';
                                },
                                rotate: dateLabels.length > 15 ? 45 : 0, // 如果标签太多，旋转标签
                                // 确保标签不会被裁剪
                                hideOverlap: false,
                                showMaxLabel: true
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            splitNumber: 4,
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false
                            },
                            axisLabel: {
                                color: this.theme ? '#ccc' : '#999',
                                fontSize: 10
                            },
                            splitLine: {
                                lineStyle: {
                                    color: this.theme ? '#444' : '#f5f5f5',
                                    type: 'dashed'
                                }
                            }
                        }
                    ],
                    series: [
                        {
                            name: '紧急',
                            type: 'bar',
                            stack: 'level',
                            emphasis: {
                                focus: 'series'
                            },
                            barWidth: '60%',
                            itemStyle: {
                                borderRadius: [0, 0, 0, 0],
                                opacity: 0.85
                            },
                            data: emerData
                        },
                        {
                            name: '重要',
                            type: 'bar',
                            stack: 'level',
                            emphasis: {
                                focus: 'series'
                            },
                            itemStyle: {
                                opacity: 0.85
                            },
                            data: majorData
                        },
                        {
                            name: '一般',
                            type: 'bar',
                            stack: 'level',
                            emphasis: {
                                focus: 'series'
                            },
                            itemStyle: {
                                borderRadius: [4, 4, 0, 0],
                                opacity: 0.85
                            },
                            data: genericData
                        }
                    ]
                });
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.statistics-container {
    background-color: #fff;
    min-height: 100vh;
}

.content-wrapper {
    padding: 32rpx 28rpx;
    background-color: #fff;
}

/* 顶部区域样式 */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 36rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.header-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 20rpx;
    letter-spacing: 1rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 28rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

/* 时间筛选器样式 */
.time-filter {
    display: flex;
    border-radius: 6rpx;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1rpx solid #f0f0f0;
    width: 160rpx;
    height: 56rpx;
}

.filter-item {
    flex: 1;
    text-align: center;
    line-height: 56rpx;
    font-size: 24rpx;
    color: #999;
    transition: all 0.3s ease;
    position: relative;

    &.active {
        color: #007AFF;
        font-weight: 500;
        background-color: rgba(0, 122, 255, 0.05);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3rpx;
            background-color: #007AFF;
        }
    }
}

/* 数据概览卡片样式 */
.data-overview {
    display: flex;
    justify-content: space-between;
    margin-bottom: 36rpx;

    &.mini-cards {
        margin-bottom: 24rpx;
    }
}

.data-card {
    flex: 1;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 36rpx 24rpx;
    text-align: center;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
    margin: 0 12rpx;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:first-child {
        margin-left: 0;
    }

    &:last-child {
        margin-right: 0;
    }

    &.processing-card {
        // border-left: 3rpx solid #FF9500;
        background-color: rgba(255, 149, 0, 0.03);
    }

    &.resolved-card {
        // border-left: 3rpx solid #34C759;
        background-color: rgba(52, 199, 89, 0.03);
    }

    &.timeout-card {
        // border-left: 3rpx solid #FF3B30;
        background-color: rgba(255, 59, 48, 0.03);
    }

    &.mini {
        padding: 24rpx;
        box-shadow: none;
        background-color: #fff;
        border: 1rpx solid #f0f0f0;
        position: relative;

        &::after {
            display: none;
        }
    }

    &.emer-card {
        // border-left: 3rpx solid #FF3B30;
        background-color: rgba(255, 59, 48, 0.03);
    }

    &.major-card {
        // border-left: 3rpx solid #FF9500;
        background-color: rgba(255, 149, 0, 0.03);
    }

    &.generic-card {
        // border-left: 3rpx solid #34C759;
        background-color: rgba(52, 199, 89, 0.03);
    }
}

.card-label {
    font-size: 24rpx;
    color: #999;
    margin-top: 12rpx;
    letter-spacing: 0.5rpx;
}

.card-value {
    font-size: 52rpx;
    font-weight: 500;
    margin-bottom: 4rpx;
    font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, sans-serif;

    &.processing {
        color: #FF9500;
    }

    &.resolved {
        color: #34C759;
    }

    &.timeout {
        color: #FF3B30;
    }
}

.card-value-lg {
    font-size: 44rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 8rpx;
    font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, sans-serif;

    &.emer {
        color: #FF3B30; // 紧急
    }

    &.major {
        color: #FF9500; // 重要
    }

    &.generic {
        color: #34C759; // 一般
    }
}

.card-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3rpx;

    &.emer-indicator {
        background-color: #FF3B30;
    }

    &.major-indicator {
        background-color: #FF9500;
    }

    &.generic-indicator {
        background-color: #34C759;
    }

    &.processing-indicator {
        background-color: #FF9500;
    }

    &.resolved-indicator {
        background-color: #34C759;
    }

    &.timeout-indicator {
        background-color: #FF3B30;
    }
}

/* 图表区域样式 */
.chart-section {
    margin-top: 36rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 28rpx 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.02);
    position: relative;
    border: 1rpx solid #f5f5f5;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 16rpx;
    letter-spacing: 0.5rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

.chart-container {
    height: 560rpx;
    width: 100%;
}
/* 深色模式样式 */
.statistics-container-dark {
    background-color: #2b2b2b;
    color: #fff;

    .content-wrapper {
        background-color: #2b2b2b;
    }

    .header-section {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .header-title {
        color: #fff;
    }

    .time-filter {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .filter-item {
        color: #ccc;

        &.active {
            color: #007AFF;
            background-color: rgba(0, 122, 255, 0.1);
        }
    }

    .data-card {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);

        &.mini {
            background-color: #333;
            border: 1rpx solid rgba(255, 255, 255, 0.1);
        }

        &.emer-card {
            background-color: rgba(255, 59, 48, 0.1);
        }

        &.major-card {
            background-color: rgba(255, 149, 0, 0.1);
        }

        &.generic-card {
            background-color: rgba(52, 199, 89, 0.1);
        }

        &.processing-card {
            background-color: rgba(255, 149, 0, 0.1);
        }

        &.resolved-card {
            background-color: rgba(52, 199, 89, 0.1);
        }

        &.timeout-card {
            background-color: rgba(255, 59, 48, 0.1);
        }
    }

    .card-label {
        color: #ccc;
    }

    .card-value-lg {
        color: #fff;
    }

    .chart-section {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .section-header {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        color: #fff;
    }
}
</style>