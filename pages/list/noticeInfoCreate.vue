<template>
    <view class="draft-list" :class="{ 'draft-list-dark': theme }">
        <view class="main-form">


            <!-- flow form -->
            <flow-form ref="formForm" :key="formKey" :model="formMain" :component-groups="componentGroups">
                <template #bottom>
                    <uni-section title="基本信息" type="line">
                        <view class="example" style="padding: 26rpx;padding-bottom: 16rpx;">
                            <!-- 基础表单校验 -->
                            <uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="通知编号" name="code">
                                    <uni-easyinput disabled v-model="valiFormData.code" placeholder="请输入通知编号" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="标题" required name="title">
                                    <uni-easyinput :disabled="!isCreateNotice" v-model="valiFormData.title"
                                        placeholder="请输入标题" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="创建人" name="creatorName">
                                    <uni-easyinput class="work-order-type" disabled v-model="valiFormData.creatorName"
                                        placeholder="创建人" />
                                </uni-forms-item>
                                <uni-forms-item
                                    style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
                                    label-width="130px" label="创建部门" name="deptName">
                                    <uni-easyinput class="work-order-type" disabled="!isCreateNotice"
                                        v-model="valiFormData.deptName" placeholder="创建部门" />
                                </uni-forms-item>
                                <uni-forms-item required label-width="130px" label="通知级别" name="orderLevel"
                                    style="padding-bottom: 36rpx;">
                                    <pop-select v-if="isCreateNotice" v-model="valiFormData.orderLevel"
                                        placeholder="请选择通知级别" :multiple="grabSingle" :options="noticeLevelArr"
                                        value-to-string />
                                    <uni-easyinput disabled v-else v-model="valiFormData.orderLevelName"
                                        placeholder="通知级别" />
                                </uni-forms-item>
                            </uni-forms>
                        </view>
                    </uni-section>
                    <uni-section style="margin-top: -40rpx;" title="通知详情" type="line">
                        <uni-group>
                            <uni-easyinput :disabled="!isCreateNotice" v-model="valiFormData.detail"
                                placeholder="请输入通知详情" autoHeight :maxlength="1000" type="textarea"></uni-easyinput>
                        </uni-group>
                    </uni-section>
                    <uni-section v-if="this.pageTitle != '通知详情'" title="派发对象" type="line">
                        <uni-group>

                            <uni-forms-item label-width="130px" label="组织机构" name="acceptDeptNames">
                                <ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
                                    :multiple='false' @select-change="selectChange" title="组织机构" :localdata="listData"
                                    valueKey="deptId" textKey="deptName" childrenKey="children" />
                                <view @click="showPicker">
                                    <uni-easyinput :clearable="false" @focus="handleFocus"
                                        v-model="valiFormData.acceptDeptNames" placeholder="请选择组织机构" />
                                </view>
                            </uni-forms-item>
                            <uni-forms-item label="受理人">
                                <pop-select v-if="participantOptionsView" @update:modelValue="userNamesDataUpdate"
                                    v-model="valiFormData.userIds" placeholder="请选择受理人" :multiple="true"
                                    :options="participantOptions" value-to-string>
                                    <template #label="option">
                                        <view>{{ option.realName }}</view>
                                    </template>
                                </pop-select>
                            </uni-forms-item>
                        </uni-group>
                    </uni-section>
                    <uni-section v-if="this.pageTitle != '通知详情'" title="预约派发通知单" type="line">
                        <template v-slot:right>
                            <switch @change="switchNoticeHandle" style="transform:scale(0.8);" />
                        </template>
                        <uni-group v-if="switchNotice">
                            <uni-forms-item label="选择派发时间">
                                <uni-datetime-picker class="notice-time" type="datetime"
                                    v-model="valiFormData.distributeTime" @change="changeLog" />
                            </uni-forms-item>
                        </uni-group>
                    </uni-section>
                    <uni-section v-if="this.pageTitle == '通知详情'" title="通知查看情况" type="line">
                        <noticeReadOrUnread class="notice-read-or-unread" :valiFormData="valiFormData" />
                    </uni-section>
                </template>
            </flow-form>
        </view>
        <uni-row class="main-form-bottom" :gutter="20"
            style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
            <uni-col v-if="this.pageTitle == '创建通知'" :span="12">
                <button type="primary" @click="startProcess" :loading="submitLoading">派发</button>
            </uni-col>
            <uni-col :span="this.pageTitle == '创建通知' ? 12 : 24">
                <button type="primary" @click="back">取消</button>
            </uni-col>
        </uni-row>
    </view>


</template>

<script>
import FlowForm from "./flow-form.vue"
import list from "./list.js"
import flow from "./flow.js"
import {
    noticeOrderSave, queryDepts, sysmanageUsersList,
    noticeOrderInit, noticeOrderGetById, workOrderListWorkOrderLevel
} from "./api/index.js"
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue"
import noticeReadOrUnread from './noticeReadOrUnread.vue'
export default {
    name: "draft-list",
    components: {
        FlowForm, baTreePicker, noticeReadOrUnread
    },
    mixins: [list, flow],
    data() {
        return {
            noticeLevelArr: [
                { "value": "EMER", "label": "紧急" },
                { "value": "GENERIC", "label": "普通" }
            ],
            datetimesingle: '',
            switchNotice: false,
            procKey: null,
            procName: null,

            // 表单key
            formKey: 1,
            // 表单模型对象
            formMain: {},
            // 流程模型定义信息
            processMain: {},
            // 组件分组
            componentGroups: [],

            // 基本信息
            baseParams: {},

            operateState: "todo",

            submitLoading: false,

            pageTitle: '',

            // 校验表单数据
            valiFormData: {
                code: '',
                title: '',
                creatorName: '',
                orderLevel: "",
                detail: "",
                selectedData: [],
                deptName: "",
                deptId: "",
                userNames: "",
                userIds: "",
                acceptDeptNames: "",
                acceptDeptIds: "",
                prebookDistribute: false,
                distributeTime: "",
                orderLevelName: "",
                timeoutDate: "",
                timeoutSwitch: false,
            },
            // 校验规则
            rules: {
                title: {
                    rules: [{
                        required: true,
                        errorMessage: '标题不能为空'
                    }]
                },
                orderLevel: {
                    rules: [{
                        required: true,
                        errorMessage: '通知级别不能为空'
                    }]
                },
            },
            // 当前是否为创建通知
            isCreateNotice: true,
            listData: [],
            participantOptions: [],
            participantOptionsView: true,
            theme: false,
        }
    },
    watch: {
        "valiFormData.acceptDeptIds": {
            handler(newVal, oldVal) {
                console.log('acceptDeptIds changed: ', newVal, oldVal);
                this.valiFormData.selectedData = [newVal];
                this.getSysmanageUsersList({
                    "deptId": this.valiFormData.acceptDeptIds,
                    "pageNo": 1,
                    "pageSize": 100000
                })
                this.participantOptionsView = false;
                setTimeout(() => {
                    this.participantOptionsView = true;
                }, 333);
            },
            deep: true,
            important: true
        },
        theme(newVal) {
            uni.setStorageSync('theme', newVal);
            if (newVal) {
                uni.setNavigationBarColor({
                    frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#2b2b2b', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#2b2b2b',
                    color: '#ffffff',
                    selectedColor: '#fff'
                });
            } else {
                uni.setNavigationBarColor({
                    frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#ffffff', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#ffffff',
                    color: '#000000',
                    selectedColor: '#000'
                });
            }
        }
    },
    // 页面加载
    onLoad(page) {

        workOrderListWorkOrderLevel().then(res => {
            this.noticeLevelArr = res.data;
        })

        // this.procKey = page.procKey;
        // this.procName = page.procName;
        this.pageTitle = page.type;
        uni.setNavigationBarTitle({
            title: page.type
        });
        if (page.type == '通知详情') {
            this.isCreateNotice = false;
            this.initNoticeDetailFlow(page);
        } else {
            this.initFlow();
            this.isCreateNotice = true;
        }

        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }

    },
    onShow() {
        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }
    },

    methods: {
        handleFocus() {
            uni.hideKeyboard();
            this.$nextTick(() => {
                uni.hideKeyboard(); // 隐藏键盘
                // 或通过DOM操作移开焦点
                // document.activeElement.blur();
            });
        },
        // 查看通知详情走改初始化
        initNoticeDetailFlow(page) {
            queryDepts().then((res) => {
                this.listData = res.data;
            })
            noticeOrderGetById(page.id).then(res => {
                // valiFormData.orderLevelName

                this.valiFormData = {
                    ...this.valiFormData,
                    ...res.data
                };
                this.noticeLevelArr.forEach(res => {
                    if (res.value == this.valiFormData.orderLevel) {
                        this.valiFormData.orderLevelName = res.label;
                    }
                })
            })
        },
        getSysmanageUsersList(params) {
            sysmanageUsersList(params).then(res => {
                let list = [];
                if (res?.data?.length > 0) {
                    res.data.forEach(item => {
                        list.push({
                            ...item,
                            label: item.realName,
                            value: item.userId
                        })
                    });
                }
                console.log(list)
                this.participantOptions = list;
            })
        },
        userNamesDataUpdate(userid, username) {
            console.log(userid, username);
            this.valiFormData.userNames = username;
            this.valiFormData.userIds = userid;
        },
        // 显示选择器
        showPicker() {
            this.$refs.treePicker._show();
        },
        //监听选择（ids为数组）
        selectChange(ids, names) {
            console.log(ids, names);
            this.valiFormData.selectedData = ids;
            this.valiFormData.acceptDeptNames = names;
            this.valiFormData.acceptDeptIds = ids[0];
        },
        changeLog(val) { },
        switchNoticeHandle(val) {
            this.switchNotice = !this.switchNotice;
            this.valiFormData.prebookDistribute = this.switchNotice ? 1 : 0;
        },
        switchNoticeOvertimeHandle(val) {
            this.valiFormData.timeoutSwitch = !this.valiFormData.timeoutSwitch;
        },
        back() {
            uni.navigateBack({
                delta: 1
            })
        },
        backAndRefrush() {
            let pages = getCurrentPages(); // 当前页面
            let beforePage = pages[pages.length - 2]; // 前一个页面
            uni.navigateBack({
                success: function () {
                    typeof (beforePage.refresh) == 'function' && beforePage.refresh();
                }
            });
        },
        initFlow() {
            queryDepts().then((res) => {
                this.listData = res.data;
            })
            noticeOrderInit().then(res => {
                this.valiFormData = {
                    ...this.valiFormData,
                    ...res.data,
                    prebookDistribute: 0
                };
            })

        },
        getFormattedDateTime() {
            const date = new Date();
            // 获取时间组件
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
            const day = String(date.getDate()).padStart(2, '0');        // 日期补零
            const hours = String(date.getHours()).padStart(2, '0');     // 小时补零
            const minutes = String(date.getMinutes()).padStart(2, '0');// 分钟补零
            const seconds = String(date.getSeconds()).padStart(2, '0');// 秒补零
            // 拼接为指定格式
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        startProcess() {
            if (this.submitLoading) return;


            console.log("this.valiFormData",this.valiFormData)
            let formForm = this.$refs.formForm;

            this.$refs['valiForm'].validate().then(res => {
                if (!this.valiFormData.acceptDeptNames || this.valiFormData.acceptDeptNames.length == 0) {
                    uni.showToast({
                        title: "请选择组织机构",
                        icon: "none"
                    });
                    return;
                }
                if (!this.valiFormData.userIds || this.valiFormData.userIds.length == 0) {
                    uni.showToast({
                        title: "请选择受理人",
                        icon: "none"
                    });
                    return;
                }

                if (this.switchNotice && !this.valiFormData.distributeTime) {
                    uni.showToast({
                        title: "请选择派发时间",
                        icon: "none"
                    });
                    return;
                }
                uni.showLoading({
                    title: "派发通知中...",
                    icon: "loading"
                })
                console.log('success', res);
                // uni.showToast({
                //     title: `校验通过`
                // })
                const requestForm = {
                    ...this.valiFormData,
                    realDistributeTime: this.getFormattedDateTime(),
                    timeoutSwitch: this.valiFormData.timeoutSwitch ? 1 : 0,
                }
                console.log(requestForm);
                noticeOrderSave(requestForm).then(res => {
                    if (res.data.length == 0) {
                        uni.showToast({
                            title: "通知派发成功"
                        });
                        uni.hideLoading();
                        this.backAndRefrush();
                    } else {
                        uni.showToast({
                            title: "通知派发失败" + res.data,
                            icon: "none"
                        });
                        uni.hideLoading();
                    }

                })
            }).catch(err => {
                console.log('err', err);
                // if (Array.isArray(data)) {
                //     let firstField = data[0].key;
                //     this.$refs['valiForm'].scrollPropertyToView(firstField);
                // }
                uni.showToast({
                    title: err?.[0]?.['errorMessage'],
                    duration: 1666,
                    icon: "none"
                })
            })
            // formForm.validate().then(() => {
            //     uni.showLoading({
            //         title: "提交工单...",
            //         icon: "loading"
            //     })
            //     this.submitLoading = true;
            //     startProcess(this.procKey, {
            //         ...this.baseParams,
            //         nextActivity: this.nextActivity,
            //         taskParticipant: this.taskParticipant,
            //         formMain: this.formMain
            //     }).then(res => {
            //         let resData = res.data;
            //         console.log("resData", resData);
            //         if (resData.status == '0') {
            //             uni.showToast({
            //                 title: "工单创建成功"
            //             });
            //             this.backAndRefrush();
            //         } else {
            //             uni.showToast({
            //                 title: "创建工单失败"
            //             });
            //             console.error("res", res);
            //         }
            //     }).catch(err => {
            //         console.error(err);
            //         uni.showToast({
            //             title: "创建工单失败"
            //         })
            //     }).then(() => {
            //         this.submitLoading = false;
            //     });
            // }).catch(data => {
            //     console.log(data);
            //     let msg = "校验未通过";
            //     if (Array.isArray(data)) {
            //         let firstField = data[0].key;
            //         formForm.scrollPropertyToView(firstField);
            //     }
            //     uni.showToast({
            //         title: msg,
            //         duration: 1000
            //     })
            // });
        }
    },

}
</script>

<style lang="scss" scoped>
.draft-list {
    .main-form {
        /* #ifndef H5 */
        height: calc(100vh - 60px);
        /* #endif */
        /* #ifdef H5 */
        height: calc(100vh - 100px);
        /* #endif */
        overflow: auto;
    }
}

.draft-list-dark {
    .main-form {
        /* #ifndef H5 */
        height: calc(100vh - 60px);
        /* #endif */
        /* #ifdef H5 */
        height: calc(100vh - 100px);
        /* #endif */
        overflow: auto;
    }

    background: #2b2b2b !important;

    :deep(.uni-section) {
        background: #2b2b2b;

        span {
            color: #fff;
        }
    }

    :deep(.uni-group__content) {
        background: #2b2b2b;
    }

    :deep(.uni-forms-item--border) {
        border-top: 1px #aaa6a6 solid;
    }

    :deep(.uni-forms-item__content) {
        color: #ffffffd2;
    }

    .custom-dialog-submit {
        background: #2b2b2b !important;
        color: #fff !important;

        .custom-dialog {
            background: #2b2b2b !important;
            color: #fff !important;
        }
    }

    .custom-dialog-cancelOpenDialog {
        background: #2b2b2b !important;

        .custom-dialog-new {
            background: #2b2b2b !important;
            color: #fff !important;
        }
    }

    .custom-dialog-backDialog {
        background: #2b2b2b !important;
        color: #fff !important;

        .custom-dialog {
            background: #2b2b2b !important;
            color: #fff !important;
        }
    }

    :deep(.uni-popup) {
        .uni-popup__wrapper {
            background: #2b2b2b !important;
        }
    }

    :deep(input) {
        background: #2b2b2b !important;
        color: #fff !important;
    }

    :deep(.uni-easyinput__content) {
        background: #2b2b2b !important;
        color: #fff !important;
    }

    :deep(.tree-dialog) {
        background: #2b2b2b !important;
        color: #fff !important;

        .tree-bar {
            background: #2b2b2b !important;
            color: #fff !important;

            .tree-bar-cancel {
                color: #fff !important;
            }
        }
    }

    :deep(.upload-wrap) {
        background: #2b2b2b !important;

        .btn-click {
            background: #2b2b2b !important;
            color: #fff !important;
        }
    }

    :deep(.file-line.btn-click) {
        background: #777676a4 !important;
        color: #fff !important;
    }

    :deep(.file-line) {
        background: #777676a4 !important;
        color: #fff !important;
    }

    :deep(.flow-steps__column-text) {
        color: #fff !important;

        .step-title {
            color: #fff !important;
        }

        .col uni-view {
            color: #ffffffc3 !important;
        }
    }

    .main-form-bottom {
        background: #2b2b2b !important;
        color: #fff !important;
        border-top: 1px solid #ffffff7b;
    }

    :deep(.uni-section__content-title) {
        color: #fff !important;
    }

    :deep(.uni-forms-item__label) {
        color: #fff !important;
    }

    :deep(.pop-select__input-text) {
        color: #fff !important;
    }

    :deep(.work-order-type) {
        .uni-easyinput__content {
            border: none;
        }

        .uni-input-placeholder {
            color: #ffffff !important;
            font-size: 30rpx;
        }

        .uni-easyinput__content-input {
            margin-top: 2px;
        }
    }

    :deep(.uni-date-single) {
        background: #2b2b2b !important;
        color: #fff !important;
    }

    :deep(.uni-calendar--ani-show) {
        background: #2b2b2b !important;
        color: #fff !important;

        span {
            color: #fff !important;
        }
    }

    :deep(.notice-time) {
        .uni-calendar__header-text {
            color: #fff !important;
        }

        .uni-calendar__weeks-day-text {
            color: #fff !important;
        }

        .uni-calendar-item__weeks-box-text {
            color: #fff !important;
        }

        .uni-date-changed--time-date {
            color: #fff !important;
        }

        .uni-datetime-picker-text {
            color: #fff !important;
        }
    }

    :deep(.uni-datetime-picker-popup) {
        background: #2b2b2b !important;
        color: #fff !important;

        .uni-datetime-picker-item {
            color: #fff !important;
        }
    }

    :deep(.notice-read-or-unread) {
        .v-tabs__container {
            background: #2b2b2b !important;
            color: #fff !important;
        }

        .v-tabs__container-item {
            color: #fff !important;
        }

        .active {
            color: rgb(0, 149, 255) !important;
        }

        .uni-scroll-view-content {
            color: #fff !important;
        }
    }

    :deep(.uni-forms-item) {
        border-bottom: 1px solid #ffffff62 !important;
    }

}
</style>