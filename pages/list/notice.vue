<template>
    <view class="notice" :class="{ 'notice-dark': theme }" style="min-height: 100vh;overflow: hidden;">
        <view class="notice-header" style="
                position: fixed;
                background-color: #fff;
                width: 100%;
                height: 100rpx;
                padding: 6rpx;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                padding-right: 16rpx;
                padding-left: 16rpx;
                z-index: 6;
                ">
            <uni-easyinput @change="change" @clear="change" v-model="title"
                :placeholder="'请输入通知标题'" clearable>
                <template #right>
                    <uni-icons style="padding-right: 3px ;" type="search" size="23" color="#c0c4cc" @click="change"></uni-icons>
                </template>
            </uni-easyinput>
            <!-- <uni-data-select :localdata="range" placeholder="请选择查看最近多久的通知" v-model="latestDay" :clear="false" /> -->
            <view style="line-height: 66rpx;cursor: pointer;">
                <span @click="tonoticeDetail('创建通知')"
                    style="font-size: 66rpx;font-weight: 300;padding:  0 16rpx;padding-top: 8rpx;display: inline-block;">+</span>
                <!-- <span style="vertical-align: 6rpx;" @click="tonoticeDetail('创建通知')">创建通知</span> -->
            </view>
            <view>
                <DaDropdown style="width: 53px;" v-model:dropdownMenu="dropdownMenuList" fixedTop :fixedTopValue="10"
                    @confirm="handleConfirm" @close="handleClose" @open="handleOpen">
                </DaDropdown>
            </view>

        </view>
        <!-- #ifndef APP-PLUS -->
        <view style="height: calc(var(--status-bar-height) + 100rpx);"></view>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <view style="height: calc(var(--status-bar-height) + 50rpx);"></view>
        <!-- #endif -->

        <view class="main" style="height: 100%;">
            <scroll-view class="list-content" scroll-y
                @scrolltolower="scrollBottom()">
                <uni-section v-for="item in noticeArray" :key="item.id" title="" class="notice-card">
                    <view @click="tonoticeDetail('通知详情', item)">
                        <uni-row class="notice-row" style="display: flex; flex-wrap: nowrap; align-items: center; width: 100%;">
                            <uni-col style="flex: 1; min-width: 0;">
                                <view class="notice-title">{{ item.title || '无标题' }}</view>
                            </uni-col>
                            <view style="display: flex; flex-shrink: 0; margin-left: 5px; gap: 5px;">
                                <uni-tag style="white-space: nowrap; flex-shrink: 0;" :text="getStatusText(item.status + '')" :type="item.status == '0' ?
                                    'primary' :
                                    (item.status == '1' ? 'success' :
                                        (item.status == '2' ? 'warning' : ''))"></uni-tag>
                                <uni-tag style="white-space: nowrap; flex-shrink: 0;" :text="getLevelText(item.orderLevel)" :type="getLevelColor(item.orderLevel)"></uni-tag>
                            </view>
                        </uni-row>

                        <view class="split-line"></view>

                        <view class="content-container">
                            <span class="text-ellipsis-2">{{ item.detail || '暂无' }}</span>
                        </view>

                        <view class="split-line"></view>

                        <uni-row class="notice-row notice-footer">
                            <uni-col :span="9">
                                <view class="notice-info">
                                    <span>已读：{{ item.readCount }}</span>
                                    <span style="margin-left: 10rpx;">未读：{{ item.unReadCount }}</span>
                                </view>
                            </uni-col>
                            <uni-col :span="14" :offset="1">
                                <!-- 使用不同颜色和图标区分预约时间和通知时间 -->
                                <view v-if="item.noticeStatus == '未派发'" class="time-info appointment-time">
                                    <span>预约时间：{{ item.distributeTime }}</span>
                                </view>
                                <view v-else class="time-info notice-time">
                                    <span>通知时间：{{ item.updateTime }}</span>
                                </view>
                            </uni-col>
                        </uni-row>
                    </view>
                </uni-section>

                <!-- 空数据提示 -->
                <template v-if="noticeArray.length == 0">
                    <view class="empty-text">暂无数据</view>
                </template>

                <!-- 底部空白区域 -->
                <view style="height: 13rem;"></view>
            </scroll-view>

        </view>
    </view>
</template>

<script>
import uniEasyinput from '../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue';
import { noticeOrderListPage } from "./api/index.js"
import DaDropdown from '/components/da-dropdown_2/components/da-dropdown/index.vue'
export default {
    components: { uniEasyinput, DaDropdown },
    data() {
        return {
            dropdownMenuList: [
                {
                    title: '筛选',
                    type: 'filter',
                    prop: 'conditions',
                    options: [
                        {
                            title: '工单创建时间',
                            type: 'radio',
                            prop: 'alarmOccurrenceTime',
                            options: [
                                { value: 30, label: "近一月" },
                                { value: 180, label: "近半年" },
                                { value: 360, label: "近一年" },
                            ],
                        },
                        {
                            title: '通知级别',
                            type: 'radio',
                            prop: 'orderLevel',
                            options: [
                                { value: 'EMER', label: "紧急" },
                                { value: 'MAJOR', label: "重要" },
                                { value: 'GENERIC', label: "一般" },
                            ],
                        },
                        {
                            title: '派发情况',
                            type: 'radio',
                            prop: 'status',
                            options: [
                                { value: '0', label: "未派发" },
                                { value: '1', label: "派发成功" },
                                { value: '2', label: "派发失败" },
                            ],
                        },
                    ],
                },

            ],
            title: "",
            latestDay: 30,
            orderLevel: "", // 通知级别筛选
            status: "", // 派发情况筛选
            range: [
                { value: 30, text: "近30天" },
                { value: 180, text: "近半年" },
                { value: 360, text: "近一年" },
            ],
            pageNum: 1,
            pageSize: 10,
            totalList: 0,
            noticeArray: [],
            theme: false,
        }
    },
    computed: {
        totalPage() {
            let pageSize = this.pageSize;
            let total = this.totalList;
            let n = total % pageSize;
            let totalPage = n == 0 ? total / pageSize : 1 + (total - n) / pageSize;
            return totalPage == 0 ? 1 : totalPage;
        },
    },
    onLoad(page) {
        this.currentTabIndex = Number(page.index || 0);
        this.initFlow(page);
    },
    mounted() {
        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }
    },
    onShow() {
        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }
    },
    watch: {
        latestDay: {
            handler(val) {
                console.log(val);
                this.pageNum = 1;
                this.noticeArray = [];
                this.totalList = 0;
                this.getNotices();
            }
        },
        theme(newVal) {
            uni.setStorageSync('theme', newVal);
            if (newVal) {
                uni.setNavigationBarColor({
                    frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#2b2b2b', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#2b2b2b',
                    color: '#ffffff',
                    selectedColor: '#fff'
                });
            } else {
                uni.setNavigationBarColor({
                    frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#ffffff', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#ffffff',
                    color: '#000000',
                    selectedColor: '#000'
                });
            }
        }
    },
    methods: {
        handleConfirm(v, selectedValue) {
            console.log('handleConfirm ==>', v, selectedValue.conditions)
            // 处理工单创建时间筛选
            if (selectedValue.conditions?.alarmOccurrenceTime) {
                this.latestDay = selectedValue.conditions?.alarmOccurrenceTime;
            } else {
                this.latestDay = 30;
            }

            // 处理通知级别和派发情况筛选
            this.orderLevel = selectedValue.conditions?.orderLevel || '';
            this.status = selectedValue.conditions?.status || '';

            // 重置页码并重新获取数据
            this.pageNum = 1;
            this.noticeArray = [];
            this.totalList = 0;
            this.getNotices();
        },
        handleClose(v, callbackMenuList) {
        },
        handleOpen(v) {
        },
        // 获取工单级别对应的颜色类型
        getLevelColor(type) {
            switch (type) {
                case 'EMER':
                    return 'error';
                case 'MAJOR':
                    return 'warning';
                case 'GENERIC':
                    return 'primary';
                default:
                    return 'info';
            }
        },
        // 获取工单级别对应的中文文本
        getLevelText(type) {
            switch (type) {
                case 'EMER':
                    return '紧急';
                case 'MAJOR':
                    return '重要';
                case 'GENERIC':
                    return '一般';
                default:
                    return '未知';
            }
        },
        // 获取派发状态对应的中文文本
        getStatusText(status) {
            switch (status) {
                case '0':
                    return '未派发';
                case '1':
                    return '派发成功';
                case '2':
                    return '派发失败';
                default:
                    return '暂无状态';
            }
        },
        change() {
            this.pageNum = 1;
            this.noticeArray = [];
            this.totalList = 0;
            this.getNotices();
        },
        tonoticeDetail(type, item) {
            if (item) {
                uni.navigateTo({
                    url: `/pages/list/noticeInfo?type=${type}&id=${item.id}`
                });
            } else {
                uni.navigateTo({
                    url: `/pages/list/noticeInfo?type=${type}`
                });
            }

        },
        initFlow(page) {
            this.getNotices();
        },
        getNotices() {
            // 添加loading
            uni.showLoading({
                title: '加载中...'
            });

            // 构建请求参数
            const params = {
                "pageNum": this.pageNum,
                "pageSize": this.pageSize,
                "title": this.title,
                "code": "",
                "latestDay": this.latestDay
            };

            // 添加通知级别筛选条件
            if (this.orderLevel) {
                params.orderLevel = this.orderLevel;
            }

            // 添加派发情况筛选条件
            if (this.status) {
                params.status = this.status;
            }

            noticeOrderListPage(params).then(res => {
                uni.hideLoading();
                this.totalList = res.total;
                this.noticeArray.push(...res.data);
            })
        },
        scrollBottom() {
            if (this.pageNum < this.totalPage) {
                ++this.pageNum;
                this.getNotices();
            } else {
                uni.showToast({
                    title: "没有更多数据了"
                })
            }

        },
    }
}
</script>

<style lang="scss" scoped>
/* 内容容器样式 */
/* 列表内容区域 */
.list-content {
    /* 动态计算高度，适配不同终端 */
    /* #ifdef H5 */
    height: calc(100vh - var(--window-top) - 100rpx);
    /* #endif */
    /* #ifdef APP-PLUS */
    height: calc(100vh - var(--status-bar-height) - 100rpx);
    /* #endif */
    /* #ifdef MP */
    height: calc(100vh - var(--status-bar-height) - 44px - 100rpx);
    /* #endif */
    padding-bottom: 0;
    overflow: visible !important;

    .empty-text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: gray;
    }
}

/* 卡片样式 */
.notice-card {
    margin: 5px 10px 10px;
    padding: 0;
    font-size: 13pt;
    border-radius: 16rpx;

    :deep(.uni-section-header) {
        display: none;
    }

    :deep(.uni-section-content) {
        padding: 10px 20px;
        border-radius: 16rpx;
        border: 2rpx solid #ddd;
    }
}

/* 分割线 */
.split-line {
    height: 1px;
    background-color: #E3E8F0;
    transform: scaleY(.7);
    margin: 8px 0;
}

/* 行样式 */
.notice-row {
    line-height: 25px;
}

/* 标题样式 */
.notice-title {
    font-size: 0.98em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #10172A;
}

/* 内容容器样式 */
.content-container {
    width: 100%;
    margin: 8rpx 0;
}

:deep(.uni-easyinput__content){
height: 33px;
}

.text-ellipsis-2 {
    display: -webkit-box;
    /* 启用弹性盒模型（必须为-webkit-box） */
    -webkit-box-orient: vertical;
    /* 文本垂直排列 */
    -webkit-line-clamp: 2;
    line-clamp: 2;
    /* 限制显示行数 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 溢出部分显示省略号 */
    line-height: 1.9;
    /* 行高（建议设置，避免行间距问题） */
    max-height: 3.3em;
    /* 最大高度 = 行高 × 行数（可选，增强兼容性） */
    font-size: 0.9em;
    font-weight: 300;
    color: #3C5176;
    overflow-wrap: break-word;
    /* 标准属性 */
    word-wrap: break-word;
}

/* 底部信息 */
.notice-footer {
    font-size: 0.9em;
    color: #999;
}

.notice-info {
    display: flex;
    align-items: center;
}
.notice-header-tab-bar{
    background: #fff;
}
/* 时间信息样式 */
.time-info {
    display: flex;
    align-items: center;
    white-space: nowrap;
    font-size: 0.9em;

    span {
        margin-left: 4px;
    }
}

/* 预约时间样式 */
.appointment-time {
    color: #1e89ea;
}

/* 通知时间样式 */
.notice-time {
    color: #25b47d;
}

.notice-dark {
    background-color: #2b2b2b !important;
    color: #fff;

    /* 深色模式下的卡片样式 */
    .notice-card {
        :deep(.uni-section-content) {
            background-color: #333;
            border: 1px solid #6d6c6c;
        }
    }

    /* 深色模式下的分割线 */
    .split-line {
        background-color: #444;
    }

    /* 深色模式下的标题样式 */
    .notice-title {
        color: #fff;
    }

    /* 深色模式下的内容容器样式 */
    .text-ellipsis-2 {
        color: #ffffffd1;
    }

    /* 深色模式下的底部信息 */
    .notice-footer {
        color: #ccc;
    }

    .notice-info {
        span {
            color: #ffffffea;
        }
    }

    /* 深色模式下的时间信息样式 */
    .time-info {
        span {
            color: inherit;
        }
    }

    /* 深色模式下的预约时间样式 */
    .appointment-time {
        color: #4a9ff0;
    }

    /* 深色模式下的通知时间样式 */
    .notice-time {
        color: #4cd6a0;
    }

    /* 深色模式下的头部样式 */
    .notice-header {
        background-color: #2b2b2b !important;
        color: #fff;
    }

    .notice-header-tab-bar {
        border-bottom: 2rpx solid #898888d7 !important;
        background-color: #2b2b2b !important;
    }

    /* 深色模式下的下拉菜单样式 */
    :deep(.da-dropdown-menu-item--text) {
        color: #fff;

        .is--arrdown {
            color: #fff;
        }
    }

    :deep(.da-dropdown-filter) {
        color: #fff;
        background-color: #2b2b2b !important;

        .da-dropdown-filter--title {
            color: #fff;
        }
    }

    :deep(.da-dropdown-content-popup) {
        border-bottom: 2rpx solid #aaaaaad7 !important;
    }

    /* 深色模式下的输入框样式 */
    :deep(.uni-easyinput__content) {
        background-color: #2b2b2b !important;
        color: #fff;
    }
}
</style>