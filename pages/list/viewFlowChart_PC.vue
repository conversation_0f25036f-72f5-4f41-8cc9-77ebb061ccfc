<template>
    <view class="container">
        <div ref="flowChartContainer"></div>
    </view>
</template>

<script>
import { Graph } from '@antv/x6';

export default {
    mounted() {
        this.initFlowChart();

    },
    methods: {
        initFlowChart() {
            console.log("initFlowChart", Graph)
            const graph = new Graph({
                container: this.$refs.flowChartContainer,
                width: 400, // 调整宽度
                height: 800, // 增加高度适应竖屏
                grid: { visible: true }
            });

            graph.addNode({
                id: 'startNode',
                shape: 'circle',
                x: 66,
                y: 40,
                width: 60,
                height: 60,
                label: '启动',
                attrs: {
                    body: {
                        stroke: '#237804',
                        fill: '#73d13d',
                    },
                },
            })


            graph.addNode({
                id: 'qicao',
                x: 40,
                y: 166,
                width: 120,
                height: 60,
                label: '起草工单',
                attrs: {
                    body: {
                        stroke: '#237804',
                        fill: '#73d13d',
                        rx: 10,
                        ry: 10,
                    },
                },
            })

            graph.addNode({
                id: 'chuzhi',
                x: 40,
                y: 333,
                width: 120,
                height: 60,
                label: '处置',
                attrs: {
                    body: {
                        stroke: '#237804',
                        fill: '#73d13d',
                        rx: 10,
                        ry: 10,
                    },
                },
            })




            // 添加连线（开始 -> 起草工单）
            graph.addEdge({
                source: 'startNode',
                target: 'qicao',
                attrs: {
                    line: {
                        stroke: '#333',
                        strokeWidth: 2,
                    },
                },
                labels: [
                    {
                        attrs: {
                            line: {
                                stroke: '#73d13d',
                            },
                            text: {
                                text: '新建',
                            },
                        },
                    },
                ],
            });

            graph.addEdge({
                source: 'qicao',
                target: 'chuzhi',
                attrs: {
                    line: {
                        stroke: '#333',
                        strokeWidth: 2,
                    },
                },
                labels: [
                    {
                        attrs: {
                            line: {
                                stroke: '#73d13d',
                            },
                            text: {
                                text: '处置',
                            },
                        },
                    },
                ],
            });

            graph.addEdge({
                source: 'chuzhi',
                target: 'qicao',
                vertices: [
                    { x: 266, y: 363 },
                    { x: 266, y: 195 },
                ],
                // https://x6.antv.vision/zh/docs/api/registry/connector#rounded
                connector: {
                    name: 'rounded',
                    args: {
                        radius: 2,
                    },
                },
                attrs: {
                    line: {
                        stroke: '#333',
                    },
                },
                labels: [
                    {
                        attrs: {
                            line: {
                                stroke: '#73d13d',
                            },
                            text: {
                                text: '退回',
                            },
                        },
                    },
                ],
            })

        }
    }
};
</script>

<style scoped>
.container {
    padding: 20px;
    min-height: 100vh;
}
</style>