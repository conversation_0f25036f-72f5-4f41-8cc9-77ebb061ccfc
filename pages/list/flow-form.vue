<template>
	<uni-forms ref="form" class="flow-form" :modelValue="model" :rules="formPropertyRules(componentGroups)"
		:label-width="labelWidth" :border="border">
		<uni-section v-for="(componentGroup, index) in componentGroups" :key="index" :title="componentGroup.groupName"
			type="line">
			<uni-group>
				<form-data-item v-for="(componentModel, index) in filterChildren(componentGroup.appComponent)"
					:key="index" :class="classList(componentModel)" :text-mode="textMode || componentModel.readonly"
					:form-model="model" :property-model="componentModel" :enum-request="queryEnum"></form-data-item>
			</uni-group>
		</uni-section>

		<slot name="bottom"></slot>
	</uni-forms>
</template>

<script>
import list from "./list.js"

export default {
	name: "flow-form",
	mixins: [list],
	props: {
		labelWidth: {
			type: String,
			default: "130px"
		},
		border: {
			type: Boolean,
			default: true
		},
		// 表单模型
		model: Object,
		/** 只读模式/查看详情 */
		textMode: Boolean,
		/** 组件分组 */
		componentGroups: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			formPropertyGroups: []
		}
	},
	created() {
		// load formPropertyGroups
	},
	methods: {
		queryEnum(propertyModel, queryValue) {
			let { field: code } = propertyModel;
			return null;
		},
		filterChildren(children) {
			return (children || []).filter(child => {
				let { component } = child || {};
				let { attrs } = component || {};
				attrs = attrs || {};
				let hiddenFlag = attrs.hidden;
				if (!hiddenFlag) return true;
				return hiddenFlag == '0' || hiddenFlag == 'false';
			}).map(child => {
				let { component } = child || {};
				let { attrs } = component || {};
				attrs = attrs || {};

				let readonlyFlag = !attrs.readonly ? "0" : attrs.readonly + "";
				let requiredFlag = !attrs.required ? "0" : attrs.required + "";

				let readonly = readonlyFlag == "true" || readonlyFlag == "1";
				let required = requiredFlag == "true" || requiredFlag == "1";
				return {
					...child,
					readonly,
					required
				};
			});
		},

		/**
		 * 遍历构建表单的校验模型
		 * 
		 * @param {Object} groups
		 */
		formPropertyRules(groups) {
			let formPropertyRules = {};
			for (let group of groups || []) {
				let children = this.filterChildren(group.children);
				for (let childModel of children) {
					let { required, name, field } = childModel;
					if (required) {
						formPropertyRules[field] = {
							rules: [
								{
									required: true,
									errorMessage: `${name}不能为空`
								}
							]
						}
					}
				}
			}
			return formPropertyRules;
		},

		/**
		 * 调用表单的校验方法
		 */
		validate() {
			return this.$refs.form.validate();
		},

		/**
		 * form-item 添加样式方便query selector 定位dom
		 * 
		 * @param {Object} propertyModel
		 */
		classList(propertyModel) {
			let classList = [];
			if (propertyModel.field) {
				classList.push('form-item-field-' + propertyModel.field);
			}
			return classList;
		},

		/**
		 * 将指定属性滚动到可视区域
		 * @param {Object} field
		 */
		scrollPropertyToView(field) {
			try {
				let propertyDom = this.$el.querySelector(`.form-item-field-` + field);
				if (propertyDom) {
					propertyDom.scrollIntoView();
				}
			} catch (err) {
				console.error(err);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.flow-form {}
</style>