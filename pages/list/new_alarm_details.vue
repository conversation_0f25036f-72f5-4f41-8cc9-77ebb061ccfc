<template>
	<view class="detail-list">
		<view class="main-form" style="background-color: #fff;">
			<!-- <view>
				<uni-section title="" type="" padding>
					<uni-steps class="uni-steps-Custom" :options="list1" :active="topActive">
					</uni-steps>
				</uni-section>
			</view> -->
			<!-- 流程form -->
			<button style="float: right;margin: 16rpx;margin-top: 10rpx;" @click="oneClickDeliveryOrder" type="primary" size="mini">一键派单</button>
			<flow-form :key="formKey" :model="formMain" text-mode :component-groups="componentGroups">
				<!-- <template #bottom>
				<button type="default" @click="back">关闭</button>
			</template> -->
				<template #bottom>

					<uni-section title="资源关联信息" type="line">
						<uni-group>
							<span>资源负载态势</span>
							<view style="display: flex;flex-wrap: wrap;height: 400rpx;">
								<view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
									<img src="/static/images_new/cpu.png" alt="" style="width: 100rpx;height: 100rpx;">
									<view style="font-size: 26rpx;">
										<view>73622</view>
										<view style="color: #aaa;">cpu数量</view>
									</view>
								</view>
								<view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
									<img src="/static/images_new/memory.png" alt=""
										style="width: 100rpx;height: 100rpx;">
									<view style="font-size: 26rpx;">
										<view>166 GB</view>
										<view style="color: #aaa;">内存容量</view>
									</view>
								</view>
								<view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
									<img src="/static/images_new/memory.png" alt=""
										style="width: 100rpx;height: 100rpx;">
									<view style="font-size: 26rpx;">
										<view>666 GB</view>
										<view style="color: #aaa;">磁盘容量</view>
									</view>
								</view>
								<view
									style="width: 216rpx;height: 166rpx; display: flex;flex-direction: column;flex-shrink: 0;">
									<view style="height: 166rpx; flex-shrink: 0;flex-grow: 1;">
										<l-echart style="flex-shrink: 0;" ref="chartRef01"></l-echart>
									</view>
									<view style="text-align: center; font-size: 23rpx;">cpu利用率</view>
								</view>
								<view style="width: 216rpx;height: 166rpx;">
									<view style="height: 166rpx;">
										<l-echart ref="chartRef02"></l-echart>
									</view>
									<view style="text-align: center; font-size: 23rpx;">内存利用率</view>
								</view>
								<view style="width: 216rpx;height: 166rpx;">
									<view style="height: 166rpx;">
										<l-echart ref="chartRef03"></l-echart>
									</view>
									<view style="text-align: center; font-size: 23rpx;">磁盘利用率</view>
								</view>
							</view>

							<view
								style="display: grid;grid-template-columns: repeat(3, 1fr);text-align: center;margin-top: 16rpx;cursor: pointer;">
								<view style="border: 2rpx solid #ddd;">日</view>
								<view style="border: 2rpx solid #ddd;">周</view>
								<view style="border: 2rpx solid #ddd;">月</view>
							</view>

							<view style="margin-top: 16rpx;">
								<view>
									<view style="height: 650rpx">
										<view>CPU利用率趋势图</view>
										<l-echart ref="chartLine01"></l-echart>
									</view>
									<view style="height: 650rpx">
										<view>内存利用率趋势图</view>
										<l-echart ref="chartLine02"></l-echart>
									</view>
									<view style="height: 650rpx">
										<view>磁盘利用率趋势图</view>
										<l-echart ref="chartLine03"></l-echart>
									</view>

								</view>
							</view>
						</uni-group>
					</uni-section>



					<uni-section style="padding: 0 16rpx;padding-bottom: 36rpx;" title="处置建议" type="line">
						<view style="
							width: 100%;
							min-height: 100px;
							background-color: #eee;
							border: 2rpx solid #ccc;
							padding: 16rpx 16rpx 16rpx 16rpx;
							box-sizing: border-box;
							border-radius: 16rpx;">

							<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
								:width="nvueWidth">
								<uni-col style="font-size: 23rpx;color: #666;" :span="24">
									查看任务管理器，按 Ctrl+Shift+Esc 打开任务管理器，切换到「性能」选项卡，确认 CPU 使用率是否持续高于阈值。
									切换到「进程」选项卡，按 CPU 使用率排序，找出异常占用的进程（如某个应用、后台服务或病毒程序）。
									处理方法：右键结束高占用进程（谨慎结束系统关键进程，如 System Idle Process 是正常现象）。
									关闭高负荷应用
									禁用不必要的后台应用：进入「设置」>「应用」>「后台应用」，关闭非必要程序。
									暂停系统更新 / 备份：检查 Windows 更新状态，或暂停 OneDrive、iCloud 等同步任务。
								</uni-col>
							</uni-row>
						</view>
					</uni-section>





					<uni-row :gutter="20"
						style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
						<!-- <uni-col :span="12">
							<button type="primary" @click="openDialog" :loading="submitLoading">提交</button>
						</uni-col> -->
						<uni-col :span="24">
							<button type="primary" @click="back">返回</button>
						</uni-col>

					</uni-row>
				</template>
			</flow-form>
		</view>
	</view>


</template>

<script>
import FlowSteps from "./flow-steps.vue"
import FlowForm from "./flow-form.vue"
import list from "./list.js"
import flow from "./flow.js"
import { todoDetailInfo, settings, historyAndsvg } from "./api/index.js"
// import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import * as echarts from 'echarts'
export default {
	name: "detail-list",
	components: {
		FlowForm,
		FlowSteps
	},
	mixins: [list, flow],
	data() {
		return {
			option: {
				series: [{
					type: 'gauge',
					startAngle: 90,
					endAngle: -270,
					pointer: {
						show: false
					},
					grid: {
						left: 20,
						right: 20,
						bottom: 15,
						top: 10,
						containLabel: true
					},
					progress: {
						show: true,
						overlap: false,
						roundCap: true,
						clip: false,
						itemStyle: {
							// borderWidth: 1,
							// borderColor: '#464646'
						}
					},
					axisLine: {

						lineStyle: {
							width: 6
						}
					},
					splitLine: {
						show: false,
						distance: 0,
						length: 10
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: false,
						distance: 50
					},
					data: [
						{
							value: 20,
							detail: {
								offsetCenter: ['0%', '0%']
							}
						}
					],
					detail: {
						// width: 50,
						// height: 14,
						fontSize: 13,
						//color: 'auto',
						// borderColor: 'auto',
						// borderRadius: 20,
						// borderWidth: 1,
						formatter: '{value}%'
					}
				}]
			},
			optionBrokenLine: {
				xAxis: {
					type: 'category',
					data: ['03-11', '03-13', '03-16', '03-19', '03-20', '03-22', '03-26']
				},
				yAxis: {
					type: 'value'
				},
				series: [{
					data: [150, 250, 190, 266, 333, 290, 366],
					type: 'line',
					smooth: true,
					areaStyle: {}

				}]
			},
			topActive: 0,
			list1: [
				{ desc: '周毛毛\n2018-11-11', title: '创建', },
				{ title: '告警处理', desc: '周毛毛\n2018-11-12' },
				{ title: '告警确认', desc: '周毛毛\n2018-11-13' },
				{ title: '归档', desc: '周毛毛\n2018-11-14' }
			],
			procKey: null,
			mainId: null,
			taskDefKey: null,
			taskId: null,

			// 表单key
			formKey: 1,
			// 表单模型对象
			formMain: {},
			// 流程模型定义信息
			processMain: {},
			// 组件分组
			componentGroups: [],

			// 受理人
			taskParticipant: {},
			// 抄送人
			taskParticipantcopy: {},
			// 选择分支
			nextActivity: {},
			// 基本信息
			baseParams: {},

			// 环节记录
			processLinks: [],
		}
	},
	// 页面加载
	onLoad(page) {
		// let record = this.getCurrent() || {};
		// let { procKey, mainId, taskDefKey, taskId } = record;
		// uni.setNavigationBarTitle({
		// 	title: "工单详情"
		// });

		// Object.assign(this, { procKey, mainId, taskDefKey, taskId });

		this.initFlow();
	},
	async mounted() {
		// setTimeout(() => {
		this.init();
		// }, 1000)
	},
	methods: {
		oneClickDeliveryOrder(){
			uni.navigateTo({
                url: "/pages/list/oneClickDeliveryOrder"
            });	
		},
		async init() {
			// chart 图表实例不能存在data里
			const chart01 = await this.$refs.chartRef01.init(echarts);
			chart01.setOption(this.option)
			const chart02 = await this.$refs.chartRef02.init(echarts);
			chart02.setOption(this.option)
			const chart03 = await this.$refs.chartRef03.init(echarts);
			chart03.setOption(this.option)
			this.$refs.chartLine01.init(echarts, chart => {
				chart.setOption(this.optionBrokenLine);
			});
			this.$refs.chartLine02.init(echarts, chart => {
				chart.setOption(this.optionBrokenLine);
			});
			this.$refs.chartLine03.init(echarts, chart => {
				chart.setOption(this.optionBrokenLine);
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			let pages = getCurrentPages(); // 当前页面
			let beforePage = pages[pages.length - 2]; // 前一个页面
			uni.navigateBack({
				success: function () {
					typeof (beforePage.refresh) == 'function' && beforePage.refresh();
				}
			});
		},
		initFlow() {
			// 启动详情
			// todoDetailInfo(this.procKey, this.mainId, this.taskId).then(res => {
			// 	console.log(res.data);
			// 	let { formMain, code, deployId, nextActivities } = res.data || {};
			// 	this.formMain = formMain;
			// 	this.baseParams = { code, deployId };

			// 	this.nextActivities = nextActivities;
			// 	// operateDefKey operateName
			// 	this.nextOperates = nextActivities.map(nextActivitie => {
			// 		let { operateDefKey: value, operateName: text } = nextActivitie;
			// 		return { value, text };
			// 	});
			// 	this.currentOperate = nextActivities[0] && nextActivities[0].operateDefKey;
			// 	this.nextActivity = nextActivities[0];
			// })
			// 权限配置
			// settings(this.procKey, this.mainId, this.taskDefKey).then(res => {
			// 	this.componentGroups = res.data;
			// })
			this.formMain = {
				"createByUserid": "1",
				"createByUsername": "研发管理员",
				"createByDeptcode": "md4D8i233EYqDI8wIRn",
				"createByDeptname": "研发部",
				"createTime01": "活动/清除",
				"createTime02": "未处置/已处置",
				"createTime03": "3",
				"createTime04": "交换机",
				"createTime05": "KSL_alsdk",
				"updateTime": "2025-03-14 09:43:24",
				"areaCode": "md4D8i233EYqDI8wIRn",
				"createByPhone": "2025-03-05 13:49:47",
				"id": 21,
				"sheetId": "20250305134927728",
				"title": "1233123",
				"taskDefKey": "audit",
				"taskName": "发送人验证",
				"taskId": "7629406",
				"procInstId": "7610790",
				"procDefId": "bastionMachineApply:11:6975097",
				"procKey": "bastionMachineApply",
				"status": "0",
				"statusName": "流转中",
				"expiryDate": "2025-03-05 21:49:47",
				"expiryDateCount": 1,
				"applyDept": "OxzDFiwwQz25TC8woqC",
				"applyDeptName": "部门2",
				"applyDate": "2025-03-05 13:50:07",
			}
			this.componentGroups = [
				{
					"appComponent": [
						{
							"field": "sheetId",
							"name": "工单号",
							"uid": "sheetId",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 1,
									"required": 0
								},
								"props": {
									"placeholder": "请输入工单号"
								}
							}
						},
						{
							"field": "title",
							"name": "告警标题",
							"uid": "title",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 1
								},
								"props": {
									"placeholder": "请输入标题"
								}
							}
						},
						{
							"field": "createByUsername",
							"name": "告警对象",
							"uid": "createByUsername",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入告警对象"
								}
							}
						},
						{
							"field": "createByDeptname",
							"name": "告警正文",
							"uid": "createByDeptname",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入告警正文"
								}
							}
						},
						{
							"field": "createByPhone",
							"name": "告警发生时间",
							"uid": "createByPhone",
							"component": {
								"type": "Input",
								"attrs": {
									"readonly": 1,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请输入告警发生时间"
								}
							}
						},
						{
							"field": "createTime01",
							"name": "告警状态",
							"uid": "createTime",
							"component": {
								"type": "Input",
								// 可选值为 紧急、重要、一般
								"attrs": {
									"readonly": 0,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择告警状态",

								},
							},
						},
						{
							"field": "createTime02",
							"name": "告警次数",
							"uid": "createTime",
							"component": {
								"type": "Input",
								// 可选值为 紧急、重要、一般
								"attrs": {
									"readonly": 0,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择告警次数",

								},
							},
						},
						{
							"field": "createTime03",
							"name": "设备名称",
							"uid": "createTime",
							"component": {
								"type": "Input",
								// 可选值为 紧急、重要、一般
								"attrs": {
									"readonly": 0,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择设备名称",

								},
							},
						},
						{
							"field": "createTime04",
							"name": "资源类型",
							"uid": "createTime",
							"component": {
								"type": "Input",
								// 可选值为 紧急、重要、一般
								"attrs": {
									"readonly": 0,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择资源类型",

								},
							},
						},
						{
							"field": "createTime05",
							"name": "资源位置",
							"uid": "createTime",
							"component": {
								"type": "Input",
								// 可选值为 紧急、重要、一般
								"attrs": {
									"readonly": 0,
									"hidden": 0,
									"required": 0
								},
								"props": {
									"placeholder": "请选择资源位置",

								},
							},
						},
					],
					"groupName": "基本信息"
				},
			]

			// 环节记录
			// historyAndsvg(this.procKey, this.mainId).then(res => {
			// 	try {
			// 		this.processLinks = res.data.data.processLinks;
			// 		if (this.processLinks.length > 0) {
			// 			this.processLinks = this.processLinks.slice(1);
			// 		}
			// 		this.active = this.processLinks.length - 1;
			// 	} catch (err) {
			// 		uni.showToast({
			// 			title: "获取流程记录数据错误"
			// 		})
			// 	}
			// })
		}
	}
}
</script>

<style lang="scss">
.detail-list {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}

	.process-steps {
		.item {
			.col {
				display: flex;
				margin: 10px 0;
			}
		}
	}
}
</style>