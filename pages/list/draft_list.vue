<template>
	<view class="draft-list" :class="{ 'draft-list-dark': theme }">
		<view class="main-form" style="background-color: #fff;">
			<!-- flow form -->
			<flow-form style="" ref="formForm" :key="formKey" :model="formMain" :component-groups="componentGroups">

				<template #bottom>
					<uni-section title="基本信息" type="line">
						<view class="example" style="padding: 26rpx;padding-bottom: 16rpx;">
							<!-- 基础表单校验 -->
							<uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="工单编号" name="code">
									<uni-easyinput disabled v-model="valiFormData.code" placeholder="工单编号" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="标题" required name="title">
									<uni-easyinput v-model="valiFormData.title" placeholder="请输入标题" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="工单类型" name="code">
									<uni-easyinput class="work-order-type" disabled :placeholder="procName" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="创建人" name="creatorName">
									<uni-easyinput disabled v-model="valiFormData.creatorName" placeholder="请输入创建人" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="创建部门" name="deptName">
									<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
										:multiple='false' @select-change="selectChange" title="选择部门"
										:localdata="listData" valueKey="deptId" textKey="deptName"
										childrenKey="children" />
									<view>
										<!-- @click="showPicker" -->
										<uni-easyinput disabled v-model="valiFormData.deptName" placeholder="请选择创建部门" />
									</view>
								</uni-forms-item>
								<!-- {{ orderLevelArr }} -->
								<uni-forms-item label-width="130px" label="工单级别" name="introduction">
									<pop-select v-model="valiFormData.orderLevel" placeholder="请选择工单级别"
										:options="orderLevelArr" value-to-string />
									<!-- <uni-easyinput v-model="valiFormData.introduction" placeholder="请输入通知级别" /> -->
								</uni-forms-item>
							</uni-forms>
						</view>
					</uni-section>
					<uni-section title="工单详情" type="line">
						<uni-group>
							<uni-easyinput v-model="valiFormData.detail" placeholder="请输入工单描述信息" autoHeight
								:maxlength="1000" type="textarea"></uni-easyinput>
							<uni-forms-item class="uni-forms-item__content_file" label="附件">
								<!-- <view class="example-body">
									<uni-file-picker limit="9" title="最多选择9张照片"></uni-file-picker>
								</view> -->
								<!-- <view>
									<button @click="handleUploadClick">上传</button>
									<xe-upload ref="XeUpload" :options="uploadOptions"
										@callback="handleUploadCallback"></xe-upload>
								</view> -->
								<view style="padding-bottom: 16rpx;color: #aaa;">可上传多个文件</view>
								<upload-demo :isDraft="'拟稿'" :businessId="this.valiFormData.id"
									type="file"></upload-demo>
								<!-- <l-file :businessId="this.valiFormData.id"></l-file> -->
							</uni-forms-item>
						</uni-group>

					</uni-section>
					<uni-section title="操作信息" type="line">
						<uni-group>
							<!-- <form-data-item v-for="(componentModel,index) in componentGroup.appComponent" :key="index" :text-mode="textMode" enum-label-suffix="_name" :form-model="model" :property-model="componentModel" :enum-request="queryEnum"></form-data-item> -->
							<!-- <uni-forms-item label="操作">
								<uni-data-checkbox v-model="currentOperate" :localdata="nextOperates"
									style="margin-top: 5px;" />
							</uni-forms-item> -->
							<!-- v-if="needReceiver && showDeptParticipant" -->
							<!-- <template> -->
							<uni-forms-item label-width="130px" label="组织机构" name="newdeptName">
								<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
									:multiple='false' @select-change="selectChange" title="选择组织机构" :localdata="listData"
									valueKey="deptId" textKey="deptName" childrenKey="children" />
								<view @click="showPicker">
									<uni-easyinput :clearable="false" @focus="handleFocus"
										v-model="valiFormData.newdeptName" placeholder="请选择组织机构" />
								</view>
							</uni-forms-item>
							<!-- <uni-forms-item label="组织机构">
								<pop-select v-model="taskParticipant.dept" placeholder="请选择组织机构" remote
									:request="queryDepts">
									<template #label="option">
										<view>{{ option.deptPathName }}</view>
									</template>
</pop-select>
</uni-forms-item> -->
							<!-- <uni-forms-item label="受理人">
								<pop-select :key="participantOptionsViewKey" v-if="participantOptionsView"
									@update:modelValue="userNamesDataUpdate" v-model="valiFormData.newuserNames"
									placeholder="请选择受理人" :multiple="true" :options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item> -->
							<uni-forms-item label="受理人">
								<pop-select :key="participantOptionsViewKey" v-if="participantOptionsView"
									@update:modelValue="userNamesDataUpdate" v-model="valiFormData.userIds"
									placeholder="请选择受理人" :multiple="true" :options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item>
							<uni-forms-item label=""></uni-forms-item>
							<!-- </template> -->

						</uni-group>
					</uni-section>
					<uni-section title="超时设置" type="line" style="padding-bottom: 26px;">
						<template v-slot:right>
							<switch @change="switchNoticeOvertimeHandle" style="transform:scale(0.8);" />
						</template>
						<uni-group v-if="valiFormData.timeoutSwitch">
							<uni-forms-item label="选择超时时限">
								<uni-datetime-picker class="notice-time" type="datetime"
									v-model="valiFormData.timeoutDate" @change="changeLog" />
							</uni-forms-item>
						</uni-group>
					</uni-section>
				</template>
			</flow-form>
		</view>
		<uni-row class="main-form-bottom" :gutter="20"
			style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
			<uni-col :span="12">
				<button type="primary" @click="startProcess" :loading="submitLoading">派发</button>
			</uni-col>
			<uni-col :span="12">
				<button type="default" @click="back">返回</button>
			</uni-col>
		</uni-row>
	</view>


</template>

<script>
import FlowForm from "./flow-form.vue"
import list from "./list.js"
import flow from "./flow.js"
import {
	startInfo, settings, startProcess,
	workOrderInitWorkOrder, workOrderListWorkOrderLevel,
	workOrderSubmit, queryDepts, sysmanageUsersList
} from "./api/index.js"
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue"
// import xeUpload from "/components/xe-upload_1/components/xe-upload/xe-upload.vue"
// import UploadDemo from "./UploadDemo.vue"
import UploadDemo from "/components/xe-upload_1/components/UploadDemo.vue"
// import LFile from "./LFile_New.vue"
import LFile from "/components/l-file_1-3/pages/index/index.vue"

export default {
	name: "draft-list",
	components: {
		FlowForm,
		baTreePicker,
		UploadDemo,
		LFile
	},
	mixins: [list, flow],
	data() {
		return {
			procKey: null,
			procName: null,

			// 表单key
			formKey: 1,
			// 表单模型对象
			formMain: {},
			// 流程模型定义信息
			processMain: {},
			// 组件分组
			componentGroups: [],

			// 基本信息
			baseParams: {},

			operateState: "todo",

			submitLoading: false,

			// 校验表单数据
			valiFormData: {
				code: '',
				title: '',
				creatorName: '',
				orderLevel: "",
				detail: "",
				selectedData: [],
				deptName: "",
				deptId: "",
				userNames: "",
				userIds: "",
				newdeptName: "",
				newdeptId: "",
				timeoutDate: "",
				timeoutSwitch: false,
			},
			orderLevelArr: [],
			// 校验规则
			rules: {
				title: {
					rules: [{
						required: true,
						errorMessage: '标题不能为空'
					}]
				}
			},

			listData: [],
			participantOptions: [],
			participantOptionsView: true,
			participantOptionsViewKey: 1234353245234524,

			uploadOptions: {
				// url: 'http://**************:3000/api/upload', // 不传入上传地址则返回本地链接
			},
			theme: false,
		}
	},

	watch: {
		"valiFormData.newdeptId": {
			handler(newVal, oldVal) {
				console.log('newdeptId changed: ', newVal, oldVal);
				this.valiFormData.selectedData = [newVal];
				this.getSysmanageUsersList({
					"deptId": this.valiFormData.newdeptId,
					"pageNo": 1,
					"pageSize": 100000
				})
				this.participantOptionsView = false;
				this.participantOptionsViewKey = Math.random() * 1000 * Math.random() * 1000000;
				this.$nextTick(() => {
					this.participantOptionsView = true;
					this.valiFormData.userIds = '';
				})
			},
			deep: true,
			important: true
		},
		"valiFormData.newdeptName": {
			handler(newVal, oldVal) {
				console.log("valiFormData.newdeptName", newVal.length + '');
				if (newVal.length == 0) {
					this.getSysmanageUsersList({
						"deptId": '',
						"pageNo": 1,
						"pageSize": 100000
					});
				}
			},
			deep: true,
			important: true,
			useChangeTime: false
		},
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		},
		"valiFormData.orderLevel": {
			handler(newVal, oldVal) {
				console.log("valiFormData.orderLevel", newVal);
				// 工单级别为“紧急”默认时限为1天，工单级别为“重要”默认时限为2天，工单级别为“一般”默认时限为3天
				if (!(this.useChangeTime)) {
					if (newVal == "EMER") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 1));
					}
					if (newVal == "MAJOR") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 2));
					}
					if (newVal == "GENERIC") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 3));
					}
				}
			},
			deep: true,
			important: true
		},
	},

	// 页面加载
	onLoad(page) {
		this.procKey = page.procKey;
		this.procName = page.procName;
		uni.setNavigationBarTitle({
			title: "创建 - " + this.procName
		});

		this.initFlow(page.type);
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onShow() {
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},

	methods: {
		changeLog() {
			this.useChangeTime = true;
		},
		addDays(date, days) {
			const newDate = new Date(date); // 创建副本避免副作用[7](@ref)
			newDate.setDate(newDate.getDate() + days); // 自动处理月份进位[3](@ref)
			return newDate;
		},


		formatDateTime(date, separator = '-') {
			// 补零函数复用优化[6](@ref)
			const pad = n => n.toString().padStart(2, '0');

			return [
				date.getFullYear(),
				pad(date.getMonth() + 1), // 月份补零[1](@ref)
				pad(date.getDate())
			].join(separator) + ' ' + [
				pad(date.getHours()),
				pad(date.getMinutes()),
				pad(date.getSeconds())
			].join(':');
		},
		formatUTCDateTime(date) {
			const pad = n => n.toString().padStart(2, '0');

			return [
				date.getUTCFullYear(),
				pad(date.getUTCMonth() + 1),
				pad(date.getUTCDate())
			].join('-') + ' ' + [
				pad(date.getUTCHours()),
				pad(date.getUTCMinutes()),
				pad(date.getUTCSeconds())
			].join(':');
		}

		,
		switchNoticeOvertimeHandle(val) {
			this.valiFormData.timeoutSwitch = !this.valiFormData.timeoutSwitch;
		},
		handleUploadClick() {
			// 使用默认配置则不需要传入第二个参数
			// type: ['image', 'video', 'file'];
			this.$refs.XeUpload.upload('file', {});
			// this.$refs.XeUpload.upload('image', {
			//  count: 6,
			//  sizeType: ['original', 'compressed'],
			//  sourceType: ['album'],
			// });
		},
		handleUploadCallback(e) {
			// e.type: ['choose', 'success', 'warning']
			// choose 是options没有传入url，返回临时链接时触发
			// success 是上传成功返回对应的数据时触发
			// warning 上传或者选择文件失败触发
			// ......
		},
		handleFocus() {
			uni.hideKeyboard();
			this.$nextTick(() => {
				uni.hideKeyboard(); // 隐藏键盘
				// 或通过DOM操作移开焦点
				// document.activeElement.blur();
			});
		},
		userNamesDataUpdate(userid, username) {
			console.log(userid, username);
			// if (userid?.split(',').length > 1) {
			// 	this.valiFormData.userIds = userid?.slice(2);
			// } else {
			// 	this.valiFormData.userIds = userid;
			// }
			this.valiFormData.userIds = '';
			this.valiFormData.userIds = userid;
			this.valiFormData.userNames = username;

			console.log(this.valiFormData.userNames, this.valiFormData.userIds);
		},
		// 显示选择器
		showPicker() {
			this.$refs.treePicker._show();
		},
		//监听选择（ids为数组）
		selectChange(ids, names) {
			console.log(ids, names);
			this.valiFormData.selectedData = ids;
			this.valiFormData.newdeptName = names;
			this.valiFormData.newdeptId = ids[0];
			this.valiFormData.acceptDeptNames = names;
			this.valiFormData.acceptDeptIds = ids[0];
		},
		back() {
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			let pages = getCurrentPages(); // 当前页面
			let beforePage = pages[pages.length - 2]; // 前一个页面
			uni.navigateBack({
				success: function () {
					typeof (beforePage.refresh) == 'function' && beforePage.refresh();
				}
			});
		},
		initFlow(type) {
			queryDepts().then((res) => {
				this.listData = res.data;
			})
			workOrderListWorkOrderLevel().then(res => {
				this.orderLevelArr = [...res.data];
				console.log("this.orderLevelArr", this.orderLevelArr)
			});
			// 初始化工单-初始化技术变更单信息
			workOrderInitWorkOrder(type).then(res => {

				console.log(res);
				this.valiFormData = res.data;
				this.getSysmanageUsersList({
					"deptId": this.valiFormData.deptId,
					"pageNo": 1,
					"pageSize": 100000
				});
			});
		},
		getSysmanageUsersList(params) {
			sysmanageUsersList(params).then(res => {
				let list = [];
				if (res?.data?.length > 0) {
					res.data.forEach(item => {
						list.push({
							...item,
							label: item.realName,
							value: item.userId
						})
					});
				}
				console.log(list)
				this.participantOptions = list;
			})
		},
		startProcess() {
			if (this.submitLoading) return;

			let formForm = this.$refs.formForm;
			console.log("startProcess() ", this.valiFormData)
			this.$refs['valiForm'].validate().then(res => {
				if (!this.valiFormData.acceptDeptIds || this.valiFormData?.acceptDeptIds.length == 0 || !this.valiFormData.acceptDeptNames || this.valiFormData?.acceptDeptNames.length == 0 || this.valiFormData?.newdeptName?.length == 0) {
					uni.showToast({
						title: '请选择受理人所属部门',
						duration: 2000,
						icon: 'none',
					});
					return false;
				}
				if (!this.valiFormData.userNames || this.valiFormData?.userNames.length == 0 || !this.valiFormData.userIds || this.valiFormData?.userIds.length == 0) {
					uni.showToast({
						title: '请选择受理人',
						duration: 2000,
						icon: 'none',
					});
					return false;
				}
				// return;
				uni.showLoading({
					title: "提交工单...",
					icon: "loading"
				})
				workOrderSubmit({
					...this.valiFormData,
					timeoutSwitch: this.valiFormData.timeoutSwitch ? 1 : 0,
				}).then(res => {
					uni.hideLoading();
					if (typeof res.data.length > 0) {
						uni.showToast({
							title: res.data,
							duration: 2000,
							icon: 'error',
						});
					} else {
						uni.showToast({
							title: '工单创建成功',
							duration: 2000,
							icon: 'success',
						});
						this.backAndRefrush();
					}

				});
				console.log('success', res);
				// uni.showToast({
				// 	title: `校验通过`
				// })
			}).catch(data => {
				// if (Array.isArray(data)) {
				// 	let firstField = data[0].key;
				// 	// this.$refs['valiForm'].scrollPropertyToView(firstField);
				// }
				uni.showToast({
					title: '未填写标题',
					duration: 2000,
					icon: 'error',
				})
			})
			// formForm.validate().then(() => {
			// 	uni.showLoading({
			// 		title: "提交工单...",
			// 		icon: "loading"
			// 	})
			// 	this.submitLoading = true;
			// 	startProcess(this.procKey, {
			// 		...this.baseParams,
			// 		nextActivity: this.nextActivity,
			// 		taskParticipant: this.taskParticipant,
			// 		formMain: this.formMain
			// 	}).then(res => {
			// 		let resData = res.data;
			// 		console.log("resData", resData);
			// 		if (resData.status == '0') {
			// 			uni.showToast({
			// 				title: "工单创建成功"
			// 			});
			// 			this.backAndRefrush();
			// 		} else {
			// 			uni.showToast({
			// 				title: "创建工单失败"
			// 			});
			// 			console.error("res", res);
			// 		}
			// 	}).catch(err => {
			// 		console.error(err);
			// 		uni.showToast({
			// 			title: "创建工单失败"
			// 		})
			// 	}).then(() => {
			// 		this.submitLoading = false;
			// 	});
			// }).catch(data => {
			// 	console.log(data);
			// 	let msg = "校验未通过";
			// 	if (Array.isArray(data)) {
			// 		let firstField = data[0].key;
			// 		formForm.scrollPropertyToView(firstField);
			// 	}
			// 	uni.showToast({
			// 		title: msg,
			// 		duration: 1000
			// 	})
			// });
		}
	}
}
</script>
<style lang="scss" scoped>
.uni-forms-item__content_file {
	:deep(.uni-forms-item__content) {
		width: 66px;
	}
}
</style>
<style lang="scss">
.draft-list {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;
	}
}

.draft-list-dark {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;
	}

	background: #2b2b2b !important;

	:deep(.uni-section) {
		background: #2b2b2b;

		span {
			color: #fff;
		}
	}

	:deep(.uni-group__content) {
		background: #2b2b2b;
	}

	:deep(.uni-forms-item--border) {
		border-top: 1px #aaa6a6 solid;
	}

	:deep(.uni-forms-item__content) {
		color: #ffffffd2;
	}

	.custom-dialog-submit {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-cancelOpenDialog {
		background: #2b2b2b !important;

		.custom-dialog-new {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-backDialog {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.uni-popup) {
		.uni-popup__wrapper {
			background: #2b2b2b !important;
		}
	}

	:deep(input) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-easyinput__content) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.tree-dialog) {
		background: #2b2b2b !important;
		color: #fff !important;

		.tree-bar {
			background: #2b2b2b !important;
			color: #fff !important;

			.tree-bar-cancel {
				color: #fff !important;
			}
		}
	}

	:deep(.upload-wrap) {
		background: #2b2b2b !important;

		.btn-click {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.file-line.btn-click) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.file-line) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.flow-steps__column-text) {
		color: #fff !important;

		.step-title {
			color: #fff !important;
		}

		.col uni-view {
			color: #ffffffc3 !important;
		}
	}

	.main-form-bottom {
		background: #2b2b2b !important;
		color: #fff !important;
		border-top: 1px solid #ffffff7b;
	}

	:deep(.uni-section__content-title) {
		color: #fff !important;
	}

	:deep(.uni-forms-item__label) {
		color: #fff !important;
	}

	:deep(.pop-select__input-text) {
		color: #fff !important;
	}

	:deep(.work-order-type) {
		.uni-easyinput__content {
			border: none;
		}

		.uni-input-placeholder {
			color: #ffffff !important;
			font-size: 30rpx;
		}

		.uni-easyinput__content-input {
			margin-top: 2px;
		}
	}

	:deep(.notice-time) {
		.uni-calendar__content {
			background: #2b2b2b !important;
		}

		.uni-calendar__header-text {
			color: #fff !important;
		}

		.uni-calendar__weeks-day-text {
			color: #fff !important;
		}

		.uni-calendar-item__weeks-box-text {
			color: #fff !important;
		}

		.uni-date-changed--time-date {
			color: #fff !important;
		}

		.uni-datetime-picker-text {
			color: #fff !important;
		}
	}

	:deep(.uni-date-single) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-datetime-picker-popup) {
		background: #2b2b2b !important;
		color: #fff !important;

		.uni-datetime-picker-item {
			color: #fff !important;
		}
	}

}
</style>