<template>
  <view class="container">
    <!-- 顶部选项卡 -->
    <v-tabs v-model="activeTab" :tabs="tabs" :scroll="true" :line-animation="true" :line-scale="0.7"
      active-color="#007aff" line-color="#007aff" @change="handleTabChange" />
    <!-- 内容区域（带动画） -->
    <swiper :current="activeTab" @change="swiperChange" :duration="300" class="content-swiper">
      <swiper-item v-for="(tab, index) in tabs" :key="index" class="swiper-item">
        <!-- 加载状态 -->
        <!-- <view v-if="!loadedData[index]" class="loading-wrapper">
          <view class="skeleton-line"></view>
          <view class="skeleton-line"></view>
          <view class="skeleton-line"></view>
          <view class="skeleton-line"></view>
          <view class="skeleton-line"></view>
        </view> -->

        <!-- 内容区域 -->
        <scroll-view style="padding: 26rpx;box-sizing: border-box;" scroll-y class="content-scroll">
          <view v-if="tabData[index] && tabData[index].length > 0"
            style="padding-bottom: 16rpx;padding-top: 16rpx; border-bottom: 2rpx solid #ddd;" class="content-title"
            v-for="item in tabData[index]">{{ item.userName }}</view>
          <view v-else>暂无数据</view>
        </scroll-view>

        <!-- 错误提示 -->
        <!-- <view v-if="error[index]" class="error-tip">
          <text>加载失败</text>
          <button @click="retryLoad(index)" class="retry-btn">重试</button>
        </view> -->
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
// 引入组件
import vTabs from '/components/v-tabs/v-tabs.vue'

export default {
  props: {
    valiFormData: {
      type: Object,
      required: true
    }
  },
  watch: {
    valiFormData: {
      handler(val) {
        this.loadTabData(this.currentIndex);
      },
      deep: true
    }
  },
  components: { vTabs },
  data() {
    return {
      activeTab: 0,
      tabs: ['未读', '已读'],
      loadedData: [],      // 已加载的索引
      tabData: [],         // 存储各Tab数据
      showContent: [],     // 显示动画控制
      error: [],           // 错误状态
      loading: false,       // 防止重复请求
      currentIndex: 0,
    }
  },
  created() {
    // 初始化数据
    this.tabs.forEach((_, index) => {
      this.$set(this.loadedData, index, false)
      this.$set(this.error, index, false)
      this.$set(this.showContent, index, false)
    })
    // 加载初始数据
    this.loadTabData(0)
  },
  methods: {
    // 处理Tab切换
    handleTabChange(index) {
      this.currentIndex = index;
      if (!this.loadedData[index]) {
        this.loadTabData(index)
      } else {
        this.playContentAnimation(index)
      }
    },

    // Swiper滑动同步
    swiperChange(e) {
      this.currentIndex = e.detail.current;
      this.activeTab = e.detail.current
      console.log(e.detail.current);
      if (!this.loadedData[e.detail.current]) {
        this.loadTabData(e.detail.current)
      } else {
        this.playContentAnimation(e.detail.current)
      }
    },

    // 加载数据方法
    async loadTabData(index) {
      try {
        this.$set(this.error, index, false)
        this.loading = true

        // 模拟API请求


        // 模拟数据
        // const mockData = {
        //   title: `${this.tabs[index]}新闻标题`,
        //   content: '这里是详细的新闻内容...'.repeat(20),
        //   cover: 'https://picsum.photos/300/200?random=' + index
        // }
        const mockData = [];
        this.valiFormData?.recipientInfos?.map(item => {
          if (index == 0 && item.readStatus == '未读') {
            mockData.push({ ...item })
          } else if (index == 1 && item.readStatus == '已读') {
            mockData.push({ ...item })
          }
        })
        console.log(mockData);
        // 更新数据
        // this.$set(, index, mockData)
        this.tabData[index] = mockData;
        this.$set(this.loadedData, index, true)
        this.playContentAnimation(index)
      } catch (err) {
        console.error('加载失败:', err)
        this.$set(this.error, index, true)
      } finally {
        this.loading = false
      }
    },

    // 内容显示动画
    playContentAnimation(index) {
      this.$set(this.showContent, index, false)
      setTimeout(() => {
        this.$set(this.showContent, index, true)
      }, 50)
    },

    // 重试加载
    retryLoad(index) {
      this.$set(this.error, index, false)
      this.loadTabData(index)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 53vh;
  display: flex;
  flex-direction: column;
}

.content-swiper {
  flex: 1;
  height: 0; // 修复swiper高度问题
}

.swiper-item {
  // padding: 20rpx;
}

// 加载动画
@keyframes pulse {
  0% {
    opacity: 1
  }

  50% {
    opacity: 0.5
  }

  100% {
    opacity: 1
  }
}

.loading-wrapper {
  padding: 30rpx;

  .skeleton-block {
    height: 200rpx;
    background: #f0f0f0;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    animation: pulse 1.5s infinite;
  }

  .skeleton-line {
    height: 40rpx;
    background: #f0f0f0;
    margin-bottom: 20rpx;
    border-radius: 8rpx;
    animation: pulse 1.5s infinite;

    &:last-child {
      width: 70%;
    }
  }
}

// 内容动画
.fade-in {
  animation: fadeIn 0.3s ease-in;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px)
    }

    to {
      opacity: 1;
      transform: translateY(0)
    }
  }
}

// 错误提示样式
.error-tip {
  text-align: center;
  padding: 40rpx;
  color: #999;

  .retry-btn {
    margin-top: 20rpx;
    background: #FF5B16;
    color: white;
    border-radius: 50rpx;
    font-size: 28rpx;
    padding: 0 40rpx;
    height: 60rpx;
    line-height: 60rpx;
  }
}

// 内容样式
.content-scroll {
  height: 100%;

  // .content-title {
  //   font-size: 36rpx;
  //   font-weight: bold;
  //   margin-bottom: 30rpx;
  // }

  // .content-image {
  //   width: 100%;
  //   border-radius: 12rpx;
  //   margin-bottom: 30rpx;
  // }

  // .content-text {
  //   color: #666;
  //   line-height: 1.6;
  //   font-size: 30rpx;
  // }
}
</style>