<template>
    <view>
        <view class="container" :class="{ 'dark': theme }"
            style="background-color: #fff;padding: 16rpx;height: 100vh;overflow: scroll;">
            <view>处置进度-流程图 （点击查看原图）</view>
            <view>
                <!-- 图片组件，设置图片源、样式和长按事件 -->
                <image :src="imageUrl" mode="aspectFit"
                    style="width: 96vw;margin-top: 43%; display: inline-block;border: 2rpx solid #eee;box-sizing: border-box;"
                    @longpress="saveImageToAlbum" @click="previewImage"></image>
            </view>
        </view>

    </view>
</template>

<script>
import { flowExecuteGetWorkFlow } from "./api/index.js"
export default {
    data() {
        return {
            // 图片的 URL
            imageUrl: '',
            theme: false,
        };
    },
    watch: {
        theme(newVal) {
            uni.setStorageSync('theme', newVal);
            if (newVal) {
                uni.setNavigationBarColor({
                    frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#2b2b2b', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#2b2b2b',
                    color: '#ffffff',
                    selectedColor: '#fff'
                });
            } else {
                uni.setNavigationBarColor({
                    frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                    backgroundColor: '#ffffff', // 背景颜色
                    // animation: { duration: 100 } // 过渡动画
                });
                uni.setTabBarStyle({
                    backgroundColor: '#ffffff',
                    color: '#000000',
                    selectedColor: '#000'
                });
            }
        }
    },
    onShow() {
        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }
    },

    onLoad(page) {
        this.initFlowChart(page);
        this.theme = uni.getStorageSync('theme') || false;
        if (this.theme) {
            uni.setNavigationBarColor({
                frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#2b2b2b', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#2b2b2b',
                color: '#ffffff',
                selectedColor: '#fff'
            });
        } else {
            uni.setNavigationBarColor({
                frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
                backgroundColor: '#ffffff', // 背景颜色
                // animation: { duration: 100 } // 过渡动画
            });
            uni.setTabBarStyle({
                backgroundColor: '#ffffff',
                color: '#000000',
                selectedColor: '#000'
            });
        }
    },
    methods: {
        async initFlowChart(page) {
            flowExecuteGetWorkFlow(page.instanceId).then(res => {
                this.imageUrl = `data:image/jpeg;base64,${res.data}`;
            })
        },
        // 预览图片的方法
        previewImage() {
            uni.previewImage({
                urls: [this.imageUrl]
            });
        },
        // 保存图片到相册的方法
        saveImageToAlbum() {
            uni.getSetting({
                success: (res) => {
                    if (!res.authSetting['scope.writePhotosAlbum']) {
                        // 请求相册权限
                        uni.authorize({
                            scope: 'scope.writePhotosAlbum',
                            success: () => {
                                this.saveBase64ImageToAlbum();
                            },
                            fail: () => {
                                // 引导用户手动开启权限
                                uni.showModal({
                                    title: '提示',
                                    content: '请在设置中开启相册权限，以便保存图片',
                                    success: (res) => {
                                        if (res.confirm) {
                                            uni.openSetting();
                                        }
                                    }
                                });
                            }
                        });
                    } else {
                        this.saveBase64ImageToAlbum();
                    }
                }
            });
        },
        // 保存图片到相册的方法
        saveImageToAlbum_old() {
            uni.getSetting({
                success: (res) => {
                    if (!res.authSetting['scope.writePhotosAlbum']) {
                        // 请求相册权限
                        uni.authorize({
                            scope: 'scope.writePhotosAlbum',
                            success: () => {
                                this.downloadAndSaveImage();
                            },
                            fail: () => {
                                // 引导用户手动开启权限
                                uni.showModal({
                                    title: '提示',
                                    content: '请在设置中开启相册权限，以便保存图片',
                                    success: (res) => {
                                        if (res.confirm) {
                                            uni.openSetting();
                                        }
                                    }
                                });
                            }
                        });
                    } else {
                        this.downloadAndSaveImage();
                    }
                }
            });
        },
        // 下载并保存图片的方法
        downloadAndSaveImage() {
            uni.downloadFile({
                url: this.imageUrl,
                success: (res) => {
                    if (res.statusCode === 200) {
                        uni.saveImageToPhotosAlbum({
                            filePath: res.tempFilePath,
                            success: () => {
                                uni.showToast({
                                    title: '图片保存成功',
                                    icon: 'success'
                                });
                            },
                            fail: () => {
                                uni.showToast({
                                    title: '图片保存失败',
                                    icon: 'none'
                                });
                            }
                        });
                    }
                },
                fail: () => {
                    uni.showToast({
                        title: '图片下载失败',
                        icon: 'none'
                    });
                }
            });
        },
        // 保存 Base64 图片到相册的方法
        saveBase64ImageToAlbum() {
            // 去除 Base64 数据的前缀
            const base64Data = this.imageUrl.replace(/^data:image\/\w+;base64,/, '');
            const filePath = `${uni.env.USER_DATA_PATH}/temp_image.png`;
            // 将 Base64 数据转换为本地文件
            uni.getFileSystemManager().writeFile({
                filePath,
                data: base64Data,
                encoding: 'base64',
                success: () => {
                    // 保存本地文件到相册
                    uni.saveImageToPhotosAlbum({
                        filePath,
                        success: () => {
                            uni.showToast({
                                title: '图片保存成功',
                                icon: 'success'
                            });
                        },
                        fail: () => {
                            uni.showToast({
                                title: '图片保存失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: () => {
                    uni.showToast({
                        title: '图片处理失败',
                        icon: 'none'
                    });
                }
            });
        }
    }
};
</script>

<style>
.dark {
    background-color: #2b2b2b !important;
    color: #fff;
}
</style>